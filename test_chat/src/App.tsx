import {
  Chat,
  ChatInput,
  NewSessionButton,
  SessionGroups,
  SessionMessagePanel,
  SessionMessages,
  SessionMessagesHeader,
  SessionsList,
  ThemeProvider,
} from 'reachat';
import { useState } from 'react';
import './App.css';
import { chatTheme } from './theme';

// TypeScript interfaces
interface ChatMessage {
  id: string;
  question: string;
  response: string;
  createdAt: Date;
  updatedAt: Date;
}

interface ChatSession {
  id: string;
  title: string;
  createdAt: Date;
  updatedAt: Date;
  conversations: ChatMessage[];
}

// Mock data for demonstration
const mockSessions: ChatSession[] = [
  {
    id: '1',
    title: 'Getting Started with React',
    createdAt: new Date('2024-01-15T10:00:00Z'),
    updatedAt: new Date('2024-01-15T10:30:00Z'),
    conversations: [
      {
        id: '1-0',
        question: 'How do I create a new React component?',
        response:
          "To create a new React component, you can use either function components or class components. Here's a simple function component example:\n\n```jsx\nfunction MyComponent() {\n  return <div>Hello World!</div>;\n}\n```\n\nFunction components are the modern and preferred way to create components in React.",
        createdAt: new Date('2024-01-15T10:00:00Z'),
        updatedAt: new Date('2024-01-15T10:00:00Z'),
      },
      {
        id: '1-1',
        question: 'What are React hooks?',
        response:
          'React hooks are functions that let you use state and other React features in function components. The most common hooks are:\n\n- `useState`: For managing component state\n- `useEffect`: For side effects like API calls\n- `useContext`: For consuming context\n- `useReducer`: For complex state management\n\nHooks must be called at the top level of your function component.',
        createdAt: new Date('2024-01-15T10:15:00Z'),
        updatedAt: new Date('2024-01-15T10:15:00Z'),
      },
    ],
  },
  {
    id: '2',
    title: 'TypeScript Best Practices',
    createdAt: new Date('2024-01-16T14:00:00Z'),
    updatedAt: new Date('2024-01-16T14:45:00Z'),
    conversations: [
      {
        id: '2-0',
        question: 'What are the benefits of using TypeScript?',
        response:
          "TypeScript provides several benefits:\n\n1. **Type Safety**: Catch errors at compile time\n2. **Better IDE Support**: Enhanced autocomplete and refactoring\n3. **Self-documenting Code**: Types serve as documentation\n4. **Easier Refactoring**: Confident code changes\n5. **Better Team Collaboration**: Clear interfaces and contracts\n\nIt's especially valuable in large codebases and team environments.",
        createdAt: new Date('2024-01-16T14:00:00Z'),
        updatedAt: new Date('2024-01-16T14:00:00Z'),
      },
      {
        id: '2-1',
        question: 'How do I define interfaces in TypeScript?',
        response:
          'You can define interfaces using the `interface` keyword:\n\n```typescript\ninterface User {\n  id: number;\n  name: string;\n  email: string;\n  isActive?: boolean; // optional property\n}\n\n// Usage\nconst user: User = {\n  id: 1,\n  name: "John Doe",\n  email: "<EMAIL>"\n};\n```\n\nInterfaces are great for defining object shapes and contracts.',
        createdAt: new Date('2024-01-16T14:30:00Z'),
        updatedAt: new Date('2024-01-16T14:30:00Z'),
      },
    ],
  },
  {
    id: '3',
    title: 'Tailwind CSS Tips',
    createdAt: new Date('2024-01-17T09:00:00Z'),
    updatedAt: new Date('2024-01-17T09:20:00Z'),
    conversations: [
      {
        id: '3-0',
        question: 'What are the advantages of Tailwind CSS?',
        response:
          'Tailwind CSS offers several advantages:\n\n1. **Utility-First**: Build designs with utility classes\n2. **Consistent Design System**: Predefined spacing, colors, etc.\n3. **No CSS Bloat**: Only includes used styles in production\n4. **Responsive Design**: Built-in responsive utilities\n5. **Customizable**: Easy to customize through configuration\n6. **Developer Experience**: Great IntelliSense support\n\nIt promotes consistent, maintainable styling across your application.',
        createdAt: new Date('2024-01-17T09:00:00Z'),
        updatedAt: new Date('2024-01-17T09:00:00Z'),
      },
    ],
  },
  {
    id: '4',
    title: 'API Integration Patterns',
    createdAt: new Date('2024-01-18T16:00:00Z'),
    updatedAt: new Date('2024-01-18T16:25:00Z'),
    conversations: [
      {
        id: '4-0',
        question: 'How should I handle API calls in React?',
        response:
          "There are several patterns for handling API calls in React:\n\n1. **useEffect + fetch/axios**: For simple cases\n2. **Custom hooks**: Reusable API logic\n3. **React Query/SWR**: For advanced caching and synchronization\n4. **Redux Toolkit Query**: If using Redux\n\nHere's a simple example with useEffect:\n\n```jsx\nconst [data, setData] = useState(null);\nconst [loading, setLoading] = useState(true);\n\nuseEffect(() => {\n  fetch('/api/data')\n    .then(res => res.json())\n    .then(data => {\n      setData(data);\n      setLoading(false);\n    });\n}, []);\n```",
        createdAt: new Date('2024-01-18T16:00:00Z'),
        updatedAt: new Date('2024-01-18T16:00:00Z'),
      },
    ],
  },
];

function App() {
  const [activeId, setActiveId] = useState<string | undefined>('1');
  const [sessions, setSessions] = useState<ChatSession[]>(mockSessions);
  const [loading, setLoading] = useState<boolean>(false);
  const [count, setCount] = useState<number>(5);

  const handleNewSession = (): void => {
    const newId = count.toString();
    const newSession: ChatSession = {
      id: newId,
      title: `New Session #${newId}`,
      createdAt: new Date(),
      updatedAt: new Date(),
      conversations: [],
    };
    setSessions([...sessions, newSession]);
    setActiveId(newId);
    setCount(count + 1);
  };

  const handleDelete = (id: string): void => {
    const updated = sessions.filter(s => s.id !== id);
    setSessions([...updated]);
    if (activeId === id) {
      setActiveId(undefined);
    }
  };

  const handleNewMessage = (message: string): void => {
    setLoading(true);
    const curr = sessions.find(s => s.id === activeId);
    if (curr) {
      const newMessage: ChatMessage = {
        id: `${curr.id}-${curr.conversations.length}`,
        question: message,
        response: 'This is an example response from the chat application.',
        createdAt: new Date(),
        updatedAt: new Date(),
      };
      const updated: ChatSession = {
        ...curr,
        conversations: [...curr.conversations, newMessage],
        updatedAt: new Date(),
      };
      setSessions([...sessions.filter(s => s.id !== activeId), updated]);
    }
    setLoading(false);
  };

  return (
    <div className="h-screen w-screen p-2">
      <ThemeProvider theme={chatTheme}>
        <Chat
          sessions={sessions}
          activeSessionId={activeId}
          isLoading={loading}
          onNewSession={handleNewSession}
          onSelectSession={setActiveId}
          onDeleteSession={handleDelete}
          onSendMessage={handleNewMessage}
        >
          <SessionsList>
            <NewSessionButton />
            <SessionGroups />
          </SessionsList>
          <SessionMessagePanel>
            <SessionMessagesHeader />
            <SessionMessages />
            <ChatInput />
          </SessionMessagePanel>
        </Chat>
      </ThemeProvider>
    </div>
  );
}

export default App;
