{"version": 3, "sources": ["../../refractor/lang/nginx.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = nginx\nnginx.displayName = 'nginx'\nnginx.aliases = []\nfunction nginx(Prism) {\n  ;(function (Prism) {\n    var variable =\n      /\\$(?:\\w[a-z\\d]*(?:_[^\\x00-\\x1F\\s\"'\\\\()$]*)?|\\{[^}\\s\"'\\\\]+\\})/i\n    Prism.languages.nginx = {\n      comment: {\n        pattern: /(^|[\\s{};])#.*/,\n        lookbehind: true,\n        greedy: true\n      },\n      directive: {\n        pattern:\n          /(^|\\s)\\w(?:[^;{}\"'\\\\\\s]|\\\\.|\"(?:[^\"\\\\]|\\\\.)*\"|'(?:[^'\\\\]|\\\\.)*'|\\s+(?:#.*(?!.)|(?![#\\s])))*?(?=\\s*[;{])/,\n        lookbehind: true,\n        greedy: true,\n        inside: {\n          string: {\n            pattern:\n              /((?:^|[^\\\\])(?:\\\\\\\\)*)(?:\"(?:[^\"\\\\]|\\\\.)*\"|'(?:[^'\\\\]|\\\\.)*')/,\n            lookbehind: true,\n            greedy: true,\n            inside: {\n              escape: {\n                pattern: /\\\\[\"'\\\\nrt]/,\n                alias: 'entity'\n              },\n              variable: variable\n            }\n          },\n          comment: {\n            pattern: /(\\s)#.*/,\n            lookbehind: true,\n            greedy: true\n          },\n          keyword: {\n            pattern: /^\\S+/,\n            greedy: true\n          },\n          // other patterns\n          boolean: {\n            pattern: /(\\s)(?:off|on)(?!\\S)/,\n            lookbehind: true\n          },\n          number: {\n            pattern: /(\\s)\\d+[a-z]*(?!\\S)/i,\n            lookbehind: true\n          },\n          variable: variable\n        }\n      },\n      punctuation: /[{};]/\n    }\n  })(Prism)\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,WAAO,UAAU;AACjB,UAAM,cAAc;AACpB,UAAM,UAAU,CAAC;AACjB,aAAS,MAAM,OAAO;AACpB;AAAC,OAAC,SAAUA,QAAO;AACjB,YAAI,WACF;AACF,QAAAA,OAAM,UAAU,QAAQ;AAAA,UACtB,SAAS;AAAA,YACP,SAAS;AAAA,YACT,YAAY;AAAA,YACZ,QAAQ;AAAA,UACV;AAAA,UACA,WAAW;AAAA,YACT,SACE;AAAA,YACF,YAAY;AAAA,YACZ,QAAQ;AAAA,YACR,QAAQ;AAAA,cACN,QAAQ;AAAA,gBACN,SACE;AAAA,gBACF,YAAY;AAAA,gBACZ,QAAQ;AAAA,gBACR,QAAQ;AAAA,kBACN,QAAQ;AAAA,oBACN,SAAS;AAAA,oBACT,OAAO;AAAA,kBACT;AAAA,kBACA;AAAA,gBACF;AAAA,cACF;AAAA,cACA,SAAS;AAAA,gBACP,SAAS;AAAA,gBACT,YAAY;AAAA,gBACZ,QAAQ;AAAA,cACV;AAAA,cACA,SAAS;AAAA,gBACP,SAAS;AAAA,gBACT,QAAQ;AAAA,cACV;AAAA;AAAA,cAEA,SAAS;AAAA,gBACP,SAAS;AAAA,gBACT,YAAY;AAAA,cACd;AAAA,cACA,QAAQ;AAAA,gBACN,SAAS;AAAA,gBACT,YAAY;AAAA,cACd;AAAA,cACA;AAAA,YACF;AAAA,UACF;AAAA,UACA,aAAa;AAAA,QACf;AAAA,MACF,GAAG,KAAK;AAAA,IACV;AAAA;AAAA;", "names": ["Prism"]}