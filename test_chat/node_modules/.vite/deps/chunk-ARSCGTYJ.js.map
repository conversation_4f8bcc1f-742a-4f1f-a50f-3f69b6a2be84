{"version": 3, "sources": ["../../highlight.js/lib/languages/angelscript.js"], "sourcesContent": ["/*\nLanguage: AngelScript\nAuthor: <PERSON> <<EMAIL>>\nCategory: scripting\nWebsite: https://www.angelcode.com/angelscript/\n*/\n\n/** @type LanguageFn */\nfunction angelscript(hljs) {\n  var builtInTypeMode = {\n    className: 'built_in',\n    begin: '\\\\b(void|bool|int|int8|int16|int32|int64|uint|uint8|uint16|uint32|uint64|string|ref|array|double|float|auto|dictionary)'\n  };\n\n  var objectHandleMode = {\n    className: 'symbol',\n    begin: '[a-zA-Z0-9_]+@'\n  };\n\n  var genericMode = {\n    className: 'keyword',\n    begin: '<', end: '>',\n    contains: [ builtInTypeMode, objectHandleMode ]\n  };\n\n  builtInTypeMode.contains = [ genericMode ];\n  objectHandleMode.contains = [ genericMode ];\n\n  return {\n    name: 'AngelScript',\n    aliases: ['asc'],\n\n    keywords:\n      'for in|0 break continue while do|0 return if else case switch namespace is cast ' +\n      'or and xor not get|0 in inout|10 out override set|0 private public const default|0 ' +\n      'final shared external mixin|10 enum typedef funcdef this super import from interface ' +\n      'abstract|0 try catch protected explicit property',\n\n    // avoid close detection with C# and JS\n    illegal: '(^using\\\\s+[A-Za-z0-9_\\\\.]+;$|\\\\bfunction\\\\s*[^\\\\(])',\n\n    contains: [\n      { // 'strings'\n        className: 'string',\n        begin: '\\'', end: '\\'',\n        illegal: '\\\\n',\n        contains: [ hljs.BACKSLASH_ESCAPE ],\n        relevance: 0\n      },\n\n      // \"\"\"heredoc strings\"\"\"\n      {\n        className: 'string',\n        begin: '\"\"\"', end: '\"\"\"'\n      },\n\n      { // \"strings\"\n        className: 'string',\n        begin: '\"', end: '\"',\n        illegal: '\\\\n',\n        contains: [ hljs.BACKSLASH_ESCAPE ],\n        relevance: 0\n      },\n\n      hljs.C_LINE_COMMENT_MODE, // single-line comments\n      hljs.C_BLOCK_COMMENT_MODE, // comment blocks\n\n      { // metadata\n        className: 'string',\n        begin: '^\\\\s*\\\\[', end: '\\\\]',\n      },\n\n      { // interface or namespace declaration\n        beginKeywords: 'interface namespace', end: /\\{/,\n        illegal: '[;.\\\\-]',\n        contains: [\n          { // interface or namespace name\n            className: 'symbol',\n            begin: '[a-zA-Z0-9_]+'\n          }\n        ]\n      },\n\n      { // class declaration\n        beginKeywords: 'class', end: /\\{/,\n        illegal: '[;.\\\\-]',\n        contains: [\n          { // class name\n            className: 'symbol',\n            begin: '[a-zA-Z0-9_]+',\n            contains: [\n              {\n                begin: '[:,]\\\\s*',\n                contains: [\n                  {\n                    className: 'symbol',\n                    begin: '[a-zA-Z0-9_]+'\n                  }\n                ]\n              }\n            ]\n          }\n        ]\n      },\n\n      builtInTypeMode, // built-in types\n      objectHandleMode, // object handles\n\n      { // literals\n        className: 'literal',\n        begin: '\\\\b(null|true|false)'\n      },\n\n      { // numbers\n        className: 'number',\n        relevance: 0,\n        begin: '(-?)(\\\\b0[xXbBoOdD][a-fA-F0-9]+|(\\\\b\\\\d+(\\\\.\\\\d*)?f?|\\\\.\\\\d+f?)([eE][-+]?\\\\d+f?)?)'\n      }\n    ]\n  };\n}\n\nmodule.exports = angelscript;\n"], "mappings": ";;;;;AAAA;AAAA;AAQA,aAAS,YAAY,MAAM;AACzB,UAAI,kBAAkB;AAAA,QACpB,WAAW;AAAA,QACX,OAAO;AAAA,MACT;AAEA,UAAI,mBAAmB;AAAA,QACrB,WAAW;AAAA,QACX,OAAO;AAAA,MACT;AAEA,UAAI,cAAc;AAAA,QAChB,WAAW;AAAA,QACX,OAAO;AAAA,QAAK,KAAK;AAAA,QACjB,UAAU,CAAE,iBAAiB,gBAAiB;AAAA,MAChD;AAEA,sBAAgB,WAAW,CAAE,WAAY;AACzC,uBAAiB,WAAW,CAAE,WAAY;AAE1C,aAAO;AAAA,QACL,MAAM;AAAA,QACN,SAAS,CAAC,KAAK;AAAA,QAEf,UACE;AAAA;AAAA,QAMF,SAAS;AAAA,QAET,UAAU;AAAA,UACR;AAAA;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,YAAM,KAAK;AAAA,YAClB,SAAS;AAAA,YACT,UAAU,CAAE,KAAK,gBAAiB;AAAA,YAClC,WAAW;AAAA,UACb;AAAA;AAAA,UAGA;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,YAAO,KAAK;AAAA,UACrB;AAAA,UAEA;AAAA;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,YAAK,KAAK;AAAA,YACjB,SAAS;AAAA,YACT,UAAU,CAAE,KAAK,gBAAiB;AAAA,YAClC,WAAW;AAAA,UACb;AAAA,UAEA,KAAK;AAAA;AAAA,UACL,KAAK;AAAA;AAAA,UAEL;AAAA;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,YAAY,KAAK;AAAA,UAC1B;AAAA,UAEA;AAAA;AAAA,YACE,eAAe;AAAA,YAAuB,KAAK;AAAA,YAC3C,SAAS;AAAA,YACT,UAAU;AAAA,cACR;AAAA;AAAA,gBACE,WAAW;AAAA,gBACX,OAAO;AAAA,cACT;AAAA,YACF;AAAA,UACF;AAAA,UAEA;AAAA;AAAA,YACE,eAAe;AAAA,YAAS,KAAK;AAAA,YAC7B,SAAS;AAAA,YACT,UAAU;AAAA,cACR;AAAA;AAAA,gBACE,WAAW;AAAA,gBACX,OAAO;AAAA,gBACP,UAAU;AAAA,kBACR;AAAA,oBACE,OAAO;AAAA,oBACP,UAAU;AAAA,sBACR;AAAA,wBACE,WAAW;AAAA,wBACX,OAAO;AAAA,sBACT;AAAA,oBACF;AAAA,kBACF;AAAA,gBACF;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,UAEA;AAAA;AAAA,UACA;AAAA;AAAA,UAEA;AAAA;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,UACT;AAAA,UAEA;AAAA;AAAA,YACE,WAAW;AAAA,YACX,WAAW;AAAA,YACX,OAAO;AAAA,UACT;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}