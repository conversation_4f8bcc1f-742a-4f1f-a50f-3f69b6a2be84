{"version": 3, "sources": ["../../highlight.js/lib/languages/abnf.js"], "sourcesContent": ["/**\n * @param {string} value\n * @returns {RegExp}\n * */\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction source(re) {\n  if (!re) return null;\n  if (typeof re === \"string\") return re;\n\n  return re.source;\n}\n\n/**\n * @param {...(RegExp | string) } args\n * @returns {string}\n */\nfunction concat(...args) {\n  const joined = args.map((x) => source(x)).join(\"\");\n  return joined;\n}\n\n/*\nLanguage: Augmented Backus-Naur Form\nAuthor: <PERSON> <<EMAIL>>\nWebsite: https://tools.ietf.org/html/rfc5234\nAudit: 2020\n*/\n\n/** @type LanguageFn */\nfunction abnf(hljs) {\n  const regexes = {\n    ruleDeclaration: /^[a-zA-Z][a-zA-Z0-9-]*/,\n    unexpectedChars: /[!@#$^&',?+~`|:]/\n  };\n\n  const keywords = [\n    \"ALPHA\",\n    \"BIT\",\n    \"CHAR\",\n    \"CR\",\n    \"CRLF\",\n    \"CTL\",\n    \"DIGIT\",\n    \"DQUOTE\",\n    \"HEXDIG\",\n    \"HTAB\",\n    \"LF\",\n    \"LWSP\",\n    \"OCTET\",\n    \"SP\",\n    \"VCHAR\",\n    \"WSP\"\n  ];\n\n  const commentMode = hljs.COMMENT(/;/, /$/);\n\n  const terminalBinaryMode = {\n    className: \"symbol\",\n    begin: /%b[0-1]+(-[0-1]+|(\\.[0-1]+)+){0,1}/\n  };\n\n  const terminalDecimalMode = {\n    className: \"symbol\",\n    begin: /%d[0-9]+(-[0-9]+|(\\.[0-9]+)+){0,1}/\n  };\n\n  const terminalHexadecimalMode = {\n    className: \"symbol\",\n    begin: /%x[0-9A-F]+(-[0-9A-F]+|(\\.[0-9A-F]+)+){0,1}/\n  };\n\n  const caseSensitivityIndicatorMode = {\n    className: \"symbol\",\n    begin: /%[si]/\n  };\n\n  const ruleDeclarationMode = {\n    className: \"attribute\",\n    begin: concat(regexes.ruleDeclaration, /(?=\\s*=)/)\n  };\n\n  return {\n    name: 'Augmented Backus-Naur Form',\n    illegal: regexes.unexpectedChars,\n    keywords: keywords,\n    contains: [\n      ruleDeclarationMode,\n      commentMode,\n      terminalBinaryMode,\n      terminalDecimalMode,\n      terminalHexadecimalMode,\n      caseSensitivityIndicatorMode,\n      hljs.QUOTE_STRING_MODE,\n      hljs.NUMBER_MODE\n    ]\n  };\n}\n\nmodule.exports = abnf;\n"], "mappings": ";;;;;AAAA;AAAA;AASA,aAAS,OAAO,IAAI;AAClB,UAAI,CAAC,GAAI,QAAO;AAChB,UAAI,OAAO,OAAO,SAAU,QAAO;AAEnC,aAAO,GAAG;AAAA,IACZ;AAMA,aAAS,UAAU,MAAM;AACvB,YAAM,SAAS,KAAK,IAAI,CAAC,MAAM,OAAO,CAAC,CAAC,EAAE,KAAK,EAAE;AACjD,aAAO;AAAA,IACT;AAUA,aAAS,KAAK,MAAM;AAClB,YAAM,UAAU;AAAA,QACd,iBAAiB;AAAA,QACjB,iBAAiB;AAAA,MACnB;AAEA,YAAM,WAAW;AAAA,QACf;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAEA,YAAM,cAAc,KAAK,QAAQ,KAAK,GAAG;AAEzC,YAAM,qBAAqB;AAAA,QACzB,WAAW;AAAA,QACX,OAAO;AAAA,MACT;AAEA,YAAM,sBAAsB;AAAA,QAC1B,WAAW;AAAA,QACX,OAAO;AAAA,MACT;AAEA,YAAM,0BAA0B;AAAA,QAC9B,WAAW;AAAA,QACX,OAAO;AAAA,MACT;AAEA,YAAM,+BAA+B;AAAA,QACnC,WAAW;AAAA,QACX,OAAO;AAAA,MACT;AAEA,YAAM,sBAAsB;AAAA,QAC1B,WAAW;AAAA,QACX,OAAO,OAAO,QAAQ,iBAAiB,UAAU;AAAA,MACnD;AAEA,aAAO;AAAA,QACL,MAAM;AAAA,QACN,SAAS,QAAQ;AAAA,QACjB;AAAA,QACA,UAAU;AAAA,UACR;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA,KAAK;AAAA,UACL,KAAK;AAAA,QACP;AAAA,MACF;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}