{"version": 3, "sources": ["../../refractor/lang/ignore.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = ignore\nignore.displayName = 'ignore'\nignore.aliases = ['gitignore', 'hgignore', 'npmignore']\nfunction ignore(Prism) {\n  ;(function (Prism) {\n    Prism.languages.ignore = {\n      // https://git-scm.com/docs/gitignore\n      comment: /^#.*/m,\n      entry: {\n        pattern: /\\S(?:.*(?:(?:\\\\ )|\\S))?/,\n        alias: 'string',\n        inside: {\n          operator: /^!|\\*\\*?|\\?/,\n          regex: {\n            pattern: /(^|[^\\\\])\\[[^\\[\\]]*\\]/,\n            lookbehind: true\n          },\n          punctuation: /\\//\n        }\n      }\n    }\n    Prism.languages.gitignore = Prism.languages.ignore\n    Prism.languages.hgignore = Prism.languages.ignore\n    Prism.languages.npmignore = Prism.languages.ignore\n  })(Prism)\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,WAAO,UAAU;AACjB,WAAO,cAAc;AACrB,WAAO,UAAU,CAAC,aAAa,YAAY,WAAW;AACtD,aAAS,OAAO,OAAO;AACrB;AAAC,OAAC,SAAUA,QAAO;AACjB,QAAAA,OAAM,UAAU,SAAS;AAAA;AAAA,UAEvB,SAAS;AAAA,UACT,OAAO;AAAA,YACL,SAAS;AAAA,YACT,OAAO;AAAA,YACP,QAAQ;AAAA,cACN,UAAU;AAAA,cACV,OAAO;AAAA,gBACL,SAAS;AAAA,gBACT,YAAY;AAAA,cACd;AAAA,cACA,aAAa;AAAA,YACf;AAAA,UACF;AAAA,QACF;AACA,QAAAA,OAAM,UAAU,YAAYA,OAAM,UAAU;AAC5C,QAAAA,OAAM,UAAU,WAAWA,OAAM,UAAU;AAC3C,QAAAA,OAAM,UAAU,YAAYA,OAAM,UAAU;AAAA,MAC9C,GAAG,KAAK;AAAA,IACV;AAAA;AAAA;", "names": ["Prism"]}