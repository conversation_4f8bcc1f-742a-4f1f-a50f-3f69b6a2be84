{"version": 3, "sources": ["../../reachat/src/SessionMessages/SessionMessage/MessageFile/renderers/DefaultFileRenderer.tsx"], "sourcesContent": ["import { FC, ReactElement } from 'react';\nimport FileIcon from '@/assets/file.svg?react';\nimport { Ellipsis, cn } from 'reablocks';\n\ninterface DefaultFileRendererProps {\n  /**\n   * Limit for the name.\n   */\n  limit?: number;\n\n  /**\n   * Name of the file.\n   */\n  name?: string;\n\n  /**\n   * URL of the file.\n   */\n  url: string;\n\n  /**\n   * Icon to for file type.\n   */\n  fileIcon?: ReactElement;\n}\n\n/**\n * Default renderer for unspecified file types.\n */\nconst DefaultFileRenderer: FC<DefaultFileRendererProps> = ({\n  name,\n  limit = 100,\n  fileIcon = <FileIcon />,\n}) => (\n  <figure className=\"flex items-center gap-2\">\n    {fileIcon}\n    {name && (\n      <figcaption className={cn('file-name-class')}>\n        <Ellipsis value={name} limit={limit} />\n      </figcaption>\n    )}\n  </figure>\n);\n\nexport default DefaultFileRenderer;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6BA,IAAM,sBAAoD,CAAC;EACzD;EACA,QAAQ;EACR,eAAA,wBAAYA,SAAS,CAAA,CAAA;AACvB,UACE,yBAAC,UAAO,EAAA,WAAU,2BACf,UAAA;EAAA;EACA,YACC,wBAAC,cAAW,EAAA,WAAW,GAAG,iBAAiB,GACzC,cAAA,wBAAC,UAAS,EAAA,OAAO,MAAM,MAAc,CAAA,EACvC,CAAA;AAAA,EAEJ,CAAA;", "names": ["FileIcon"]}