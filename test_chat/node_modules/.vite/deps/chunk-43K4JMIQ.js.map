{"version": 3, "sources": ["../../refractor/lang/haml.js"], "sourcesContent": ["'use strict'\nvar refractorRuby = require('./ruby.js')\nmodule.exports = haml\nhaml.displayName = 'haml'\nhaml.aliases = []\nfunction haml(Prism) {\n  Prism.register(refractorRuby)\n  /* TODO\nHandle multiline code after tag\n%foo= some |\nmultiline |\ncode |\n*/\n  ;(function (Prism) {\n    Prism.languages.haml = {\n      // Multiline stuff should appear before the rest\n      'multiline-comment': {\n        pattern:\n          /((?:^|\\r?\\n|\\r)([\\t ]*))(?:\\/|-#).*(?:(?:\\r?\\n|\\r)\\2[\\t ].+)*/,\n        lookbehind: true,\n        alias: 'comment'\n      },\n      'multiline-code': [\n        {\n          pattern:\n            /((?:^|\\r?\\n|\\r)([\\t ]*)(?:[~-]|[&!]?=)).*,[\\t ]*(?:(?:\\r?\\n|\\r)\\2[\\t ].*,[\\t ]*)*(?:(?:\\r?\\n|\\r)\\2[\\t ].+)/,\n          lookbehind: true,\n          inside: Prism.languages.ruby\n        },\n        {\n          pattern:\n            /((?:^|\\r?\\n|\\r)([\\t ]*)(?:[~-]|[&!]?=)).*\\|[\\t ]*(?:(?:\\r?\\n|\\r)\\2[\\t ].*\\|[\\t ]*)*/,\n          lookbehind: true,\n          inside: Prism.languages.ruby\n        }\n      ],\n      // See at the end of the file for known filters\n      filter: {\n        pattern:\n          /((?:^|\\r?\\n|\\r)([\\t ]*)):[\\w-]+(?:(?:\\r?\\n|\\r)(?:\\2[\\t ].+|\\s*?(?=\\r?\\n|\\r)))+/,\n        lookbehind: true,\n        inside: {\n          'filter-name': {\n            pattern: /^:[\\w-]+/,\n            alias: 'symbol'\n          }\n        }\n      },\n      markup: {\n        pattern: /((?:^|\\r?\\n|\\r)[\\t ]*)<.+/,\n        lookbehind: true,\n        inside: Prism.languages.markup\n      },\n      doctype: {\n        pattern: /((?:^|\\r?\\n|\\r)[\\t ]*)!!!(?: .+)?/,\n        lookbehind: true\n      },\n      tag: {\n        // Allows for one nested group of braces\n        pattern:\n          /((?:^|\\r?\\n|\\r)[\\t ]*)[%.#][\\w\\-#.]*[\\w\\-](?:\\([^)]+\\)|\\{(?:\\{[^}]+\\}|[^{}])+\\}|\\[[^\\]]+\\])*[\\/<>]*/,\n        lookbehind: true,\n        inside: {\n          attributes: [\n            {\n              // Lookbehind tries to prevent interpolations from breaking it all\n              // Allows for one nested group of braces\n              pattern: /(^|[^#])\\{(?:\\{[^}]+\\}|[^{}])+\\}/,\n              lookbehind: true,\n              inside: Prism.languages.ruby\n            },\n            {\n              pattern: /\\([^)]+\\)/,\n              inside: {\n                'attr-value': {\n                  pattern: /(=\\s*)(?:\"(?:\\\\.|[^\\\\\"\\r\\n])*\"|[^)\\s]+)/,\n                  lookbehind: true\n                },\n                'attr-name': /[\\w:-]+(?=\\s*!?=|\\s*[,)])/,\n                punctuation: /[=(),]/\n              }\n            },\n            {\n              pattern: /\\[[^\\]]+\\]/,\n              inside: Prism.languages.ruby\n            }\n          ],\n          punctuation: /[<>]/\n        }\n      },\n      code: {\n        pattern: /((?:^|\\r?\\n|\\r)[\\t ]*(?:[~-]|[&!]?=)).+/,\n        lookbehind: true,\n        inside: Prism.languages.ruby\n      },\n      // Interpolations in plain text\n      interpolation: {\n        pattern: /#\\{[^}]+\\}/,\n        inside: {\n          delimiter: {\n            pattern: /^#\\{|\\}$/,\n            alias: 'punctuation'\n          },\n          ruby: {\n            pattern: /[\\s\\S]+/,\n            inside: Prism.languages.ruby\n          }\n        }\n      },\n      punctuation: {\n        pattern: /((?:^|\\r?\\n|\\r)[\\t ]*)[~=\\-&!]+/,\n        lookbehind: true\n      }\n    }\n    var filter_pattern =\n      '((?:^|\\\\r?\\\\n|\\\\r)([\\\\t ]*)):{{filter_name}}(?:(?:\\\\r?\\\\n|\\\\r)(?:\\\\2[\\\\t ].+|\\\\s*?(?=\\\\r?\\\\n|\\\\r)))+' // Non exhaustive list of available filters and associated languages\n    var filters = [\n      'css',\n      {\n        filter: 'coffee',\n        language: 'coffeescript'\n      },\n      'erb',\n      'javascript',\n      'less',\n      'markdown',\n      'ruby',\n      'scss',\n      'textile'\n    ]\n    var all_filters = {}\n    for (var i = 0, l = filters.length; i < l; i++) {\n      var filter = filters[i]\n      filter =\n        typeof filter === 'string'\n          ? {\n              filter: filter,\n              language: filter\n            }\n          : filter\n      if (Prism.languages[filter.language]) {\n        all_filters['filter-' + filter.filter] = {\n          pattern: RegExp(\n            filter_pattern.replace('{{filter_name}}', function () {\n              return filter.filter\n            })\n          ),\n          lookbehind: true,\n          inside: {\n            'filter-name': {\n              pattern: /^:[\\w-]+/,\n              alias: 'symbol'\n            },\n            text: {\n              pattern: /[\\s\\S]+/,\n              alias: [filter.language, 'language-' + filter.language],\n              inside: Prism.languages[filter.language]\n            }\n          }\n        }\n      }\n    }\n    Prism.languages.insertBefore('haml', 'filter', all_filters)\n  })(Prism)\n}\n"], "mappings": ";;;;;;;;AAAA;AAAA;AACA,QAAI,gBAAgB;AACpB,WAAO,UAAU;AACjB,SAAK,cAAc;AACnB,SAAK,UAAU,CAAC;AAChB,aAAS,KAAK,OAAO;AACnB,YAAM,SAAS,aAAa;AAO3B,OAAC,SAAUA,QAAO;AACjB,QAAAA,OAAM,UAAU,OAAO;AAAA;AAAA,UAErB,qBAAqB;AAAA,YACnB,SACE;AAAA,YACF,YAAY;AAAA,YACZ,OAAO;AAAA,UACT;AAAA,UACA,kBAAkB;AAAA,YAChB;AAAA,cACE,SACE;AAAA,cACF,YAAY;AAAA,cACZ,QAAQA,OAAM,UAAU;AAAA,YAC1B;AAAA,YACA;AAAA,cACE,SACE;AAAA,cACF,YAAY;AAAA,cACZ,QAAQA,OAAM,UAAU;AAAA,YAC1B;AAAA,UACF;AAAA;AAAA,UAEA,QAAQ;AAAA,YACN,SACE;AAAA,YACF,YAAY;AAAA,YACZ,QAAQ;AAAA,cACN,eAAe;AAAA,gBACb,SAAS;AAAA,gBACT,OAAO;AAAA,cACT;AAAA,YACF;AAAA,UACF;AAAA,UACA,QAAQ;AAAA,YACN,SAAS;AAAA,YACT,YAAY;AAAA,YACZ,QAAQA,OAAM,UAAU;AAAA,UAC1B;AAAA,UACA,SAAS;AAAA,YACP,SAAS;AAAA,YACT,YAAY;AAAA,UACd;AAAA,UACA,KAAK;AAAA;AAAA,YAEH,SACE;AAAA,YACF,YAAY;AAAA,YACZ,QAAQ;AAAA,cACN,YAAY;AAAA,gBACV;AAAA;AAAA;AAAA,kBAGE,SAAS;AAAA,kBACT,YAAY;AAAA,kBACZ,QAAQA,OAAM,UAAU;AAAA,gBAC1B;AAAA,gBACA;AAAA,kBACE,SAAS;AAAA,kBACT,QAAQ;AAAA,oBACN,cAAc;AAAA,sBACZ,SAAS;AAAA,sBACT,YAAY;AAAA,oBACd;AAAA,oBACA,aAAa;AAAA,oBACb,aAAa;AAAA,kBACf;AAAA,gBACF;AAAA,gBACA;AAAA,kBACE,SAAS;AAAA,kBACT,QAAQA,OAAM,UAAU;AAAA,gBAC1B;AAAA,cACF;AAAA,cACA,aAAa;AAAA,YACf;AAAA,UACF;AAAA,UACA,MAAM;AAAA,YACJ,SAAS;AAAA,YACT,YAAY;AAAA,YACZ,QAAQA,OAAM,UAAU;AAAA,UAC1B;AAAA;AAAA,UAEA,eAAe;AAAA,YACb,SAAS;AAAA,YACT,QAAQ;AAAA,cACN,WAAW;AAAA,gBACT,SAAS;AAAA,gBACT,OAAO;AAAA,cACT;AAAA,cACA,MAAM;AAAA,gBACJ,SAAS;AAAA,gBACT,QAAQA,OAAM,UAAU;AAAA,cAC1B;AAAA,YACF;AAAA,UACF;AAAA,UACA,aAAa;AAAA,YACX,SAAS;AAAA,YACT,YAAY;AAAA,UACd;AAAA,QACF;AACA,YAAI,iBACF;AACF,YAAI,UAAU;AAAA,UACZ;AAAA,UACA;AAAA,YACE,QAAQ;AAAA,YACR,UAAU;AAAA,UACZ;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF;AACA,YAAI,cAAc,CAAC;AACnB,iBAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,IAAI,GAAG,KAAK;AAC9C,cAAI,SAAS,QAAQ,CAAC;AACtB,mBACE,OAAO,WAAW,WACd;AAAA,YACE;AAAA,YACA,UAAU;AAAA,UACZ,IACA;AACN,cAAIA,OAAM,UAAU,OAAO,QAAQ,GAAG;AACpC,wBAAY,YAAY,OAAO,MAAM,IAAI;AAAA,cACvC,SAAS;AAAA,gBACP,eAAe,QAAQ,mBAAmB,WAAY;AACpD,yBAAO,OAAO;AAAA,gBAChB,CAAC;AAAA,cACH;AAAA,cACA,YAAY;AAAA,cACZ,QAAQ;AAAA,gBACN,eAAe;AAAA,kBACb,SAAS;AAAA,kBACT,OAAO;AAAA,gBACT;AAAA,gBACA,MAAM;AAAA,kBACJ,SAAS;AAAA,kBACT,OAAO,CAAC,OAAO,UAAU,cAAc,OAAO,QAAQ;AAAA,kBACtD,QAAQA,OAAM,UAAU,OAAO,QAAQ;AAAA,gBACzC;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACF;AACA,QAAAA,OAAM,UAAU,aAAa,QAAQ,UAAU,WAAW;AAAA,MAC5D,GAAG,KAAK;AAAA,IACV;AAAA;AAAA;", "names": ["Prism"]}