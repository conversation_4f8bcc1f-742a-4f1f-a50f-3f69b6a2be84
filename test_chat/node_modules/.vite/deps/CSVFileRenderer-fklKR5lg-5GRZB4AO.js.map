{"version": 3, "sources": ["../../reachat/src/utils/sanitize.ts", "../../reachat/src/utils/parseCSV.ts", "../../reachat/src/assets/download.svg", "../../reachat/src/SessionMessages/SessionMessage/MessageFile/renderers/CSVFileRenderer.tsx"], "sourcesContent": ["\n/**\n * Sanitizes cell content to prevent CSV injection and other potential vulnerabilities.\n * Based on the documentation of OWASP for CSV Injection\n * https://owasp.org/www-community/attacks/CSV_Injection\n * @param cell The cell content to sanitize.\n * @returns The sanitized cell content.\n */\nexport const sanitizeSVGCell = (cell: string): string => {\n  const trimmed = cell.trim();\n  // Escape double quotes by doubling them\n  const escaped = trimmed.replace(/\"/g, '\"\"');\n  // Add single quote prefix only for potentially dangerous content\n  const prefix = /^[=+\\-@]/.test(trimmed) ? '\\'' : '';\n  // Only wrap in quotes if the content contains special characters\n  const needsQuotes = /[\",\\n\\r]/.test(escaped) || prefix;\n\n  return needsQuotes ? `\"${prefix}${escaped}\"` : escaped;\n};\n", "import { sanitizeSVGCell } from './sanitize';\n\n/**\n * Parses a CSV string from a local file and returns an array of rows.\n * Sanitizes cell data to prevent injection attacks.\n * @param csvString The raw CSV string content to parse.\n * @returns The parsed CSV data as a 2D array of strings.\n */\nexport const parseCSV = (csvString: string): string[][] => {\n  try {\n    const rows = csvString.split('\\n');\n    return rows.map((row) => row.split(',').map((cell) => sanitizeSVGCell(cell)));\n  } catch (error) {\n    console.error('Error parsing CSV:', error);\n    throw new Error('Failed to parse CSV file.');\n  }\n};\n", "import * as React from \"react\";\nconst SvgDownload = (props) => /* @__PURE__ */ React.createElement(\"svg\", { xmlns: \"http://www.w3.org/2000/svg\", width: 24, height: 24, viewBox: \"0 0 24 24\", fill: \"none\", stroke: \"currentColor\", strokeWidth: 1, strokeLinecap: \"round\", strokeLinejoin: \"round\", className: \"lucide lucide-cloud-download\", ...props }, /* @__PURE__ */ React.createElement(\"path\", { d: \"M4 14.899A7 7 0 1 1 15.71 8h1.79a4.5 4.5 0 0 1 2.5 8.242\" }), /* @__PURE__ */ React.createElement(\"path\", { d: \"M12 12v9\" }), /* @__PURE__ */ React.createElement(\"path\", { d: \"m8 17 4 4 4-4\" }));\nexport default SvgDownload;\n", "import { FC, useEffect, useState, ReactElement, useRef } from 'react';\nimport { motion, AnimatePresence } from 'motion/react';\nimport { parseCSV } from '@/utils/parseCSV';\nimport DownloadIcon from '@/assets/download.svg?react';\nimport PlaceholderIcon from '@/assets/copy.svg?react';\nimport { IconButton } from 'reablocks';\n\ninterface CSVFileRendererProps {\n  /**\n   * Name of the file.\n   */\n  name?: string;\n\n  /**\n   * URL of the file.\n   */\n  url: string;\n\n  /**\n   * Icon to for file type.\n   */\n  fileIcon?: ReactElement;\n}\n\n/**\n * Renderer for CSV files that fetches and displays a snippet of the file data.\n */\nconst CSVFileRenderer: FC<CSVFileRendererProps> = ({ name, url, fileIcon }) => {\n  const [isLoading, setIsLoading] = useState<boolean>(true);\n  const [csvData, setCsvData] = useState<string[][]>([]);\n  const [error, setError] = useState<string | null>(null);\n  const [isModalOpen, setIsModalOpen] = useState(false);\n  const modalRef = useRef<HTMLDivElement>(null);\n\n  useEffect(() => {\n    const fetchCsvData = async () => {\n      try {\n        setIsLoading(true);\n        const response = await fetch(url);\n        const data = parseCSV(await response.text());\n        setCsvData(data);\n      } catch {\n        setError('Failed to load CSV file.');\n      } finally {\n        setIsLoading(false);\n      }\n    };\n\n    fetchCsvData();\n  }, [url]);\n\n  const toggleModal = () => {\n    setIsModalOpen(prev => !prev);\n  };\n\n  const handleClickOutside = (event: MouseEvent) => {\n    if (modalRef.current && !modalRef.current.contains(event.target as Node)) {\n      setIsModalOpen(false);\n    }\n  };\n\n  useEffect(() => {\n    if (isModalOpen) {\n      document.addEventListener('mousedown', handleClickOutside);\n    } else {\n      document.removeEventListener('mousedown', handleClickOutside);\n    }\n    return () => {\n      document.removeEventListener('mousedown', handleClickOutside);\n    };\n  }, [isModalOpen]);\n\n  const downloadCSV = () => {\n    if (csvData.length === 0) return;\n\n    const csvContent = csvData.map(row => row.join(',')).join('\\n');\n    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });\n    const url = URL.createObjectURL(blob);\n    const link = document.createElement('a');\n    link.href = url;\n    link.setAttribute('download', `${name || 'data'}`);\n    document.body.appendChild(link);\n    link.click();\n    document.body.removeChild(link);\n  };\n\n  const renderTable = (data: string[][], maxRows?: number) => (\n    <motion.table\n      layout\n      className=\"w-full\"\n      transition={{ type: 'spring', stiffness: 100, damping: 20 }}\n    >\n      <thead className=\"sticky top-0 bg-gray-200 dark:bg-gray-800 z-10\">\n        <tr>\n          <th className=\"py-4 px-6\">#</th>\n          {data[0].map((header, index) => (\n            <th key={`header-${index}`} className=\"py-4 px-6\">\n              {header}\n            </th>\n          ))}\n        </tr>\n      </thead>\n      <tbody>\n        {data.slice(1, maxRows).map((row, rowIndex) => (\n          <tr\n            key={`row-${rowIndex}`}\n            className=\"border-b border-panel-accent light:border-gray-700 hover:bg-panel-accent hover:light:bg-gray-700/40 transition-colors text-base\"\n          >\n            <td className=\"py-4 px-6\">{rowIndex + 1}</td>\n            {row.map((cell, cellIndex) => (\n              <td key={`cell-${rowIndex}-${cellIndex}`} className=\"py-4 px-6\">\n                {cell}\n              </td>\n            ))}\n          </tr>\n        ))}\n      </tbody>\n    </motion.table>\n  );\n\n  return (\n    <div className=\"flex flex-col gap-2\">\n      <div className=\"flex justify-between items-center gap-4\">\n        <div className=\"csv-icon flex items-center\">\n          {fileIcon}\n          {name && <figcaption className=\"ml-1\">{name}</figcaption>}\n        </div>\n        <div className=\"csv-icon flex items-center gap-6\">\n          <IconButton size=\"small\" variant=\"text\" onClick={downloadCSV}>\n            <DownloadIcon />\n          </IconButton>\n          <IconButton size=\"small\" variant=\"text\" onClick={toggleModal}>\n            <PlaceholderIcon />\n          </IconButton>\n        </div>\n      </div>\n\n      {error && <div className=\"error-message\">{error}</div>}\n\n      {isLoading && !csvData && (\n        <div className=\"text-text-secondary\">Loading...</div>\n      )}\n\n      <div className=\"flex justify-between\">\n        {!error && csvData.length > 0 && renderTable(csvData, 6)}\n      </div>\n\n      <AnimatePresence>\n        {isModalOpen && (\n          <motion.div\n            className=\"fixed inset-0 bg-black/70 flex justify-center items-center z-50\"\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            exit={{ opacity: 0 }}\n            transition={{ duration: 0.3 }}\n          >\n            <motion.div\n              ref={modalRef}\n              className=\"bg-white dark:bg-gray-900 rounded-md w-11/12 h-5/6 overflow-auto\"\n              initial={{ scale: 0.8 }}\n              animate={{ scale: 1 }}\n              exit={{ scale: 0.8 }}\n              transition={{ duration: 0.3 }}\n            >\n              {!error && csvData.length > 0 && renderTable(csvData)}\n            </motion.div>\n          </motion.div>\n        )}\n      </AnimatePresence>\n    </div>\n  );\n};\n\nexport default CSVFileRenderer;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQa,IAAA,kBAAkB,CAAC,SAAyB;AACjD,QAAA,UAAU,KAAK,KAAK;AAE1B,QAAM,UAAU,QAAQ,QAAQ,MAAM,IAAI;AAE1C,QAAM,SAAS,WAAW,KAAK,OAAO,IAAI,MAAO;AAEjD,QAAM,cAAc,WAAW,KAAK,OAAO,KAAK;AAEhD,SAAO,cAAc,IAAI,MAAM,GAAG,OAAO,MAAM;AACjD;ACVa,IAAA,WAAW,CAAC,cAAkC;AACrD,MAAA;AACI,UAAA,OAAO,UAAU,MAAM,IAAI;AACjC,WAAO,KAAK,IAAI,CAAC,QAAQ,IAAI,MAAM,GAAG,EAAE,IAAI,CAAC,SAAS,gBAAgB,IAAI,CAAC,CAAC;EAAA,SACrE,OAAO;AACN,YAAA,MAAM,sBAAsB,KAAK;AACnC,UAAA,IAAI,MAAM,2BAA2B;EAAA;AAE/C;ACfA,IAAM,cAAc,CAAC,UAAgC,oBAAc,OAAO,EAAE,OAAO,8BAA8B,OAAO,IAAI,QAAQ,IAAI,SAAS,aAAa,MAAM,QAAQ,QAAQ,gBAAgB,aAAa,GAAG,eAAe,SAAS,gBAAgB,SAAS,WAAW,gCAAgC,GAAG,MAAO,GAAwB,oBAAc,QAAQ,EAAE,GAAG,2DAA4D,CAAA,GAAyB,oBAAc,QAAQ,EAAE,GAAG,WAAU,CAAE,GAAyB,oBAAc,QAAQ,EAAE,GAAG,gBAAiB,CAAA,CAAC;AC0B/iB,IAAM,kBAA4C,CAAC,EAAE,MAAM,KAAK,SAAA,MAAe;AAC7E,QAAM,CAAC,WAAW,YAAY,QAAI,uBAAkB,IAAI;AACxD,QAAM,CAAC,SAAS,UAAU,QAAI,uBAAqB,CAAA,CAAE;AACrD,QAAM,CAAC,OAAO,QAAQ,QAAI,uBAAwB,IAAI;AACtD,QAAM,CAAC,aAAa,cAAc,QAAI,uBAAS,KAAK;AAC9C,QAAA,eAAW,qBAAuB,IAAI;AAE5C,8BAAU,MAAM;AACd,UAAM,eAAe,YAAY;AAC3B,UAAA;AACF,qBAAa,IAAI;AACX,cAAA,WAAW,MAAM,MAAM,GAAG;AAChC,cAAM,OAAO,SAAS,MAAM,SAAS,KAAA,CAAM;AAC3C,mBAAW,IAAI;MAAA,QACT;AACN,iBAAS,0BAA0B;MAAA,UAAA;AAEnC,qBAAa,KAAK;MAAA;IAEtB;AAEa,iBAAA;EAAA,GACZ,CAAC,GAAG,CAAC;AAER,QAAM,cAAc,MAAM;AACT,mBAAA,CAAA,SAAQ,CAAC,IAAI;EAC9B;AAEM,QAAA,qBAAqB,CAAC,UAAsB;AAC5C,QAAA,SAAS,WAAW,CAAC,SAAS,QAAQ,SAAS,MAAM,MAAc,GAAG;AACxE,qBAAe,KAAK;IAAA;EAExB;AAEA,8BAAU,MAAM;AACd,QAAI,aAAa;AACN,eAAA,iBAAiB,aAAa,kBAAkB;IAAA,OACpD;AACI,eAAA,oBAAoB,aAAa,kBAAkB;IAAA;AAE9D,WAAO,MAAM;AACF,eAAA,oBAAoB,aAAa,kBAAkB;IAC9D;EAAA,GACC,CAAC,WAAW,CAAC;AAEhB,QAAM,cAAc,MAAM;AACpB,QAAA,QAAQ,WAAW,EAAG;AAEpB,UAAA,aAAa,QAAQ,IAAI,CAAO,QAAA,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,IAAI;AACxD,UAAA,OAAO,IAAI,KAAK,CAAC,UAAU,GAAG,EAAE,MAAM,0BAAA,CAA2B;AACjEA,UAAAA,OAAM,IAAI,gBAAgB,IAAI;AAC9B,UAAA,OAAO,SAAS,cAAc,GAAG;AACvC,SAAK,OAAOA;AACZ,SAAK,aAAa,YAAY,GAAG,QAAQ,MAAM,EAAE;AACxC,aAAA,KAAK,YAAY,IAAI;AAC9B,SAAK,MAAM;AACF,aAAA,KAAK,YAAY,IAAI;EAChC;AAEM,QAAA,cAAc,CAAC,MAAkB,gBACrC;IAAC,OAAO;IAAP;MACC,QAAM;MACN,WAAU;MACV,YAAY,EAAE,MAAM,UAAU,WAAW,KAAK,SAAS,GAAG;MAE1D,UAAA;YAAA,wBAAC,SAAM,EAAA,WAAU,kDACf,cAAA,yBAAC,MACC,EAAA,UAAA;cAAC,wBAAA,MAAA,EAAG,WAAU,aAAY,UAAC,IAAA,CAAA;UAC1B,KAAK,CAAC,EAAE,IAAI,CAAC,QAAQ,cACnB,wBAAA,MAAA,EAA2B,WAAU,aACnC,UAAA,OAAA,GADM,UAAU,KAAK,EAExB,CACD;QAAA,EAAA,CACH,EACF,CAAA;YACA,wBAAC,SACE,EAAA,UAAA,KAAK,MAAM,GAAG,OAAO,EAAE,IAAI,CAAC,KAAK,iBAChC;UAAC;UAAA;YAEC,WAAU;YAEV,UAAA;kBAAA,wBAAC,MAAG,EAAA,WAAU,aAAa,UAAA,WAAW,EAAA,CAAE;cACvC,IAAI,IAAI,CAAC,MAAM,kBACb,wBAAA,MAAA,EAAyC,WAAU,aACjD,UAAA,KAAA,GADM,QAAQ,QAAQ,IAAI,SAAS,EAEtC,CACD;YAAA;UAAA;UARI,OAAO,QAAQ;QAAA,CAUvB,EACH,CAAA;MAAA;IAAA;EACF;AAIA,aAAA,yBAAC,OAAI,EAAA,WAAU,uBACb,UAAA;QAAC,yBAAA,OAAA,EAAI,WAAU,2CACb,UAAA;UAAC,yBAAA,OAAA,EAAI,WAAU,8BACZ,UAAA;QAAA;QACA,YAAQ,wBAAC,cAAW,EAAA,WAAU,QAAQ,UAAK,KAAA,CAAA;MAAA,EAAA,CAC9C;UACA,yBAAC,OAAI,EAAA,WAAU,oCACb,UAAA;YAAC,wBAAA,YAAA,EAAW,MAAK,SAAQ,SAAQ,QAAO,SAAS,aAC/C,cAAC,wBAAAC,aAAA,CAAA,CAAa,EAChB,CAAA;YACA,wBAAC,YAAW,EAAA,MAAK,SAAQ,SAAQ,QAAO,SAAS,aAC/C,cAAC,wBAAAC,SAAA,CAAgB,CAAA,EACnB,CAAA;MAAA,EACF,CAAA;IAAA,EAAA,CACF;IAEC,aAAS,wBAAC,OAAI,EAAA,WAAU,iBAAiB,UAAM,MAAA,CAAA;IAE/C,aAAa,CAAC,eAAA,wBACZ,OAAI,EAAA,WAAU,uBAAsB,UAAU,aAAA,CAAA;QAGhD,wBAAA,OAAA,EAAI,WAAU,wBACZ,UAAC,CAAA,SAAS,QAAQ,SAAS,KAAK,YAAY,SAAS,CAAC,EACzD,CAAA;QAEA,wBAAC,iBAAA,EACE,UACC,mBAAA;MAAC,OAAO;MAAP;QACC,WAAU;QACV,SAAS,EAAE,SAAS,EAAE;QACtB,SAAS,EAAE,SAAS,EAAE;QACtB,MAAM,EAAE,SAAS,EAAE;QACnB,YAAY,EAAE,UAAU,IAAI;QAE5B,cAAA;UAAC,OAAO;UAAP;YACC,KAAK;YACL,WAAU;YACV,SAAS,EAAE,OAAO,IAAI;YACtB,SAAS,EAAE,OAAO,EAAE;YACpB,MAAM,EAAE,OAAO,IAAI;YACnB,YAAY,EAAE,UAAU,IAAI;YAE3B,UAAA,CAAC,SAAS,QAAQ,SAAS,KAAK,YAAY,OAAO;UAAA;QAAA;MACtD;IAAA,EAGN,CAAA;EAAA,EAAA,CACF;AAEJ;", "names": ["url", "DownloadIcon", "PlaceholderIcon"]}