{"version": 3, "sources": ["../../refractor/lang/concurnas.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = concurnas\nconcurnas.displayName = 'concurnas'\nconcurnas.aliases = ['conc']\nfunction concurnas(Prism) {\n  Prism.languages.concurnas = {\n    comment: {\n      pattern: /(^|[^\\\\])(?:\\/\\*[\\s\\S]*?(?:\\*\\/|$)|\\/\\/.*)/,\n      lookbehind: true,\n      greedy: true\n    },\n    langext: {\n      pattern: /\\b\\w+\\s*\\|\\|[\\s\\S]+?\\|\\|/,\n      greedy: true,\n      inside: {\n        'class-name': /^\\w+/,\n        string: {\n          pattern: /(^\\s*\\|\\|)[\\s\\S]+(?=\\|\\|$)/,\n          lookbehind: true\n        },\n        punctuation: /\\|\\|/\n      }\n    },\n    function: {\n      pattern: /((?:^|\\s)def[ \\t]+)[a-zA-Z_]\\w*(?=\\s*\\()/,\n      lookbehind: true\n    },\n    keyword:\n      /\\b(?:abstract|actor|also|annotation|assert|async|await|bool|boolean|break|byte|case|catch|changed|char|class|closed|constant|continue|def|default|del|double|elif|else|enum|every|extends|false|finally|float|for|from|global|gpudef|gpukernel|if|import|in|init|inject|int|lambda|local|long|loop|match|new|nodefault|null|of|onchange|open|out|override|package|parfor|parforsync|post|pre|private|protected|provide|provider|public|return|shared|short|single|size_t|sizeof|super|sync|this|throw|trait|trans|transient|true|try|typedef|unchecked|using|val|var|void|while|with)\\b/,\n    boolean: /\\b(?:false|true)\\b/,\n    number:\n      /\\b0b[01][01_]*L?\\b|\\b0x(?:[\\da-f_]*\\.)?[\\da-f_p+-]+\\b|(?:\\b\\d[\\d_]*(?:\\.[\\d_]*)?|\\B\\.\\d[\\d_]*)(?:e[+-]?\\d[\\d_]*)?[dfls]?/i,\n    punctuation: /[{}[\\];(),.:]/,\n    operator:\n      /<==|>==|=>|->|<-|<>|&==|&<>|\\?:?|\\.\\?|\\+\\+|--|[-+*/=<>]=?|[!^~]|\\b(?:and|as|band|bor|bxor|comp|is|isnot|mod|or)\\b=?/,\n    annotation: {\n      pattern: /@(?:\\w+:)?(?:\\w+|\\[[^\\]]+\\])?/,\n      alias: 'builtin'\n    }\n  }\n  Prism.languages.insertBefore('concurnas', 'langext', {\n    'regex-literal': {\n      pattern: /\\br(\"|')(?:\\\\.|(?!\\1)[^\\\\\\r\\n])*\\1/,\n      greedy: true,\n      inside: {\n        interpolation: {\n          pattern:\n            /((?:^|[^\\\\])(?:\\\\{2})*)\\{(?:[^{}]|\\{(?:[^{}]|\\{[^}]*\\})*\\})+\\}/,\n          lookbehind: true,\n          inside: Prism.languages.concurnas\n        },\n        regex: /[\\s\\S]+/\n      }\n    },\n    'string-literal': {\n      pattern: /(?:\\B|\\bs)(\"|')(?:\\\\.|(?!\\1)[^\\\\\\r\\n])*\\1/,\n      greedy: true,\n      inside: {\n        interpolation: {\n          pattern:\n            /((?:^|[^\\\\])(?:\\\\{2})*)\\{(?:[^{}]|\\{(?:[^{}]|\\{[^}]*\\})*\\})+\\}/,\n          lookbehind: true,\n          inside: Prism.languages.concurnas\n        },\n        string: /[\\s\\S]+/\n      }\n    }\n  })\n  Prism.languages.conc = Prism.languages.concurnas\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,WAAO,UAAU;AACjB,cAAU,cAAc;AACxB,cAAU,UAAU,CAAC,MAAM;AAC3B,aAAS,UAAU,OAAO;AACxB,YAAM,UAAU,YAAY;AAAA,QAC1B,SAAS;AAAA,UACP,SAAS;AAAA,UACT,YAAY;AAAA,UACZ,QAAQ;AAAA,QACV;AAAA,QACA,SAAS;AAAA,UACP,SAAS;AAAA,UACT,QAAQ;AAAA,UACR,QAAQ;AAAA,YACN,cAAc;AAAA,YACd,QAAQ;AAAA,cACN,SAAS;AAAA,cACT,YAAY;AAAA,YACd;AAAA,YACA,aAAa;AAAA,UACf;AAAA,QACF;AAAA,QACA,UAAU;AAAA,UACR,SAAS;AAAA,UACT,YAAY;AAAA,QACd;AAAA,QACA,SACE;AAAA,QACF,SAAS;AAAA,QACT,QACE;AAAA,QACF,aAAa;AAAA,QACb,UACE;AAAA,QACF,YAAY;AAAA,UACV,SAAS;AAAA,UACT,OAAO;AAAA,QACT;AAAA,MACF;AACA,YAAM,UAAU,aAAa,aAAa,WAAW;AAAA,QACnD,iBAAiB;AAAA,UACf,SAAS;AAAA,UACT,QAAQ;AAAA,UACR,QAAQ;AAAA,YACN,eAAe;AAAA,cACb,SACE;AAAA,cACF,YAAY;AAAA,cACZ,QAAQ,MAAM,UAAU;AAAA,YAC1B;AAAA,YACA,OAAO;AAAA,UACT;AAAA,QACF;AAAA,QACA,kBAAkB;AAAA,UAChB,SAAS;AAAA,UACT,QAAQ;AAAA,UACR,QAAQ;AAAA,YACN,eAAe;AAAA,cACb,SACE;AAAA,cACF,YAAY;AAAA,cACZ,QAAQ,MAAM,UAAU;AAAA,YAC1B;AAAA,YACA,QAAQ;AAAA,UACV;AAAA,QACF;AAAA,MACF,CAAC;AACD,YAAM,UAAU,OAAO,MAAM,UAAU;AAAA,IACzC;AAAA;AAAA;", "names": []}