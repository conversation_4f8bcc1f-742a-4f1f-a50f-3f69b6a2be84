{"version": 3, "sources": ["../../highlight.js/lib/core.js", "../../format/format.js", "../../fault/index.js", "../../lowlight/lib/core.js"], "sourcesContent": ["function deepFreeze(obj) {\n    if (obj instanceof Map) {\n        obj.clear = obj.delete = obj.set = function () {\n            throw new Error('map is read-only');\n        };\n    } else if (obj instanceof Set) {\n        obj.add = obj.clear = obj.delete = function () {\n            throw new Error('set is read-only');\n        };\n    }\n\n    // Freeze self\n    Object.freeze(obj);\n\n    Object.getOwnPropertyNames(obj).forEach(function (name) {\n        var prop = obj[name];\n\n        // Freeze prop if it is an object\n        if (typeof prop == 'object' && !Object.isFrozen(prop)) {\n            deepFreeze(prop);\n        }\n    });\n\n    return obj;\n}\n\nvar deepFreezeEs6 = deepFreeze;\nvar _default = deepFreeze;\ndeepFreezeEs6.default = _default;\n\n/** @implements CallbackResponse */\nclass Response {\n  /**\n   * @param {CompiledMode} mode\n   */\n  constructor(mode) {\n    // eslint-disable-next-line no-undefined\n    if (mode.data === undefined) mode.data = {};\n\n    this.data = mode.data;\n    this.isMatchIgnored = false;\n  }\n\n  ignoreMatch() {\n    this.isMatchIgnored = true;\n  }\n}\n\n/**\n * @param {string} value\n * @returns {string}\n */\nfunction escapeHTML(value) {\n  return value\n    .replace(/&/g, '&amp;')\n    .replace(/</g, '&lt;')\n    .replace(/>/g, '&gt;')\n    .replace(/\"/g, '&quot;')\n    .replace(/'/g, '&#x27;');\n}\n\n/**\n * performs a shallow merge of multiple objects into one\n *\n * @template T\n * @param {T} original\n * @param {Record<string,any>[]} objects\n * @returns {T} a single new object\n */\nfunction inherit(original, ...objects) {\n  /** @type Record<string,any> */\n  const result = Object.create(null);\n\n  for (const key in original) {\n    result[key] = original[key];\n  }\n  objects.forEach(function(obj) {\n    for (const key in obj) {\n      result[key] = obj[key];\n    }\n  });\n  return /** @type {T} */ (result);\n}\n\n/**\n * @typedef {object} Renderer\n * @property {(text: string) => void} addText\n * @property {(node: Node) => void} openNode\n * @property {(node: Node) => void} closeNode\n * @property {() => string} value\n */\n\n/** @typedef {{kind?: string, sublanguage?: boolean}} Node */\n/** @typedef {{walk: (r: Renderer) => void}} Tree */\n/** */\n\nconst SPAN_CLOSE = '</span>';\n\n/**\n * Determines if a node needs to be wrapped in <span>\n *\n * @param {Node} node */\nconst emitsWrappingTags = (node) => {\n  return !!node.kind;\n};\n\n/** @type {Renderer} */\nclass HTMLRenderer {\n  /**\n   * Creates a new HTMLRenderer\n   *\n   * @param {Tree} parseTree - the parse tree (must support `walk` API)\n   * @param {{classPrefix: string}} options\n   */\n  constructor(parseTree, options) {\n    this.buffer = \"\";\n    this.classPrefix = options.classPrefix;\n    parseTree.walk(this);\n  }\n\n  /**\n   * Adds texts to the output stream\n   *\n   * @param {string} text */\n  addText(text) {\n    this.buffer += escapeHTML(text);\n  }\n\n  /**\n   * Adds a node open to the output stream (if needed)\n   *\n   * @param {Node} node */\n  openNode(node) {\n    if (!emitsWrappingTags(node)) return;\n\n    let className = node.kind;\n    if (!node.sublanguage) {\n      className = `${this.classPrefix}${className}`;\n    }\n    this.span(className);\n  }\n\n  /**\n   * Adds a node close to the output stream (if needed)\n   *\n   * @param {Node} node */\n  closeNode(node) {\n    if (!emitsWrappingTags(node)) return;\n\n    this.buffer += SPAN_CLOSE;\n  }\n\n  /**\n   * returns the accumulated buffer\n  */\n  value() {\n    return this.buffer;\n  }\n\n  // helpers\n\n  /**\n   * Builds a span element\n   *\n   * @param {string} className */\n  span(className) {\n    this.buffer += `<span class=\"${className}\">`;\n  }\n}\n\n/** @typedef {{kind?: string, sublanguage?: boolean, children: Node[]} | string} Node */\n/** @typedef {{kind?: string, sublanguage?: boolean, children: Node[]} } DataNode */\n/**  */\n\nclass TokenTree {\n  constructor() {\n    /** @type DataNode */\n    this.rootNode = { children: [] };\n    this.stack = [this.rootNode];\n  }\n\n  get top() {\n    return this.stack[this.stack.length - 1];\n  }\n\n  get root() { return this.rootNode; }\n\n  /** @param {Node} node */\n  add(node) {\n    this.top.children.push(node);\n  }\n\n  /** @param {string} kind */\n  openNode(kind) {\n    /** @type Node */\n    const node = { kind, children: [] };\n    this.add(node);\n    this.stack.push(node);\n  }\n\n  closeNode() {\n    if (this.stack.length > 1) {\n      return this.stack.pop();\n    }\n    // eslint-disable-next-line no-undefined\n    return undefined;\n  }\n\n  closeAllNodes() {\n    while (this.closeNode());\n  }\n\n  toJSON() {\n    return JSON.stringify(this.rootNode, null, 4);\n  }\n\n  /**\n   * @typedef { import(\"./html_renderer\").Renderer } Renderer\n   * @param {Renderer} builder\n   */\n  walk(builder) {\n    // this does not\n    return this.constructor._walk(builder, this.rootNode);\n    // this works\n    // return TokenTree._walk(builder, this.rootNode);\n  }\n\n  /**\n   * @param {Renderer} builder\n   * @param {Node} node\n   */\n  static _walk(builder, node) {\n    if (typeof node === \"string\") {\n      builder.addText(node);\n    } else if (node.children) {\n      builder.openNode(node);\n      node.children.forEach((child) => this._walk(builder, child));\n      builder.closeNode(node);\n    }\n    return builder;\n  }\n\n  /**\n   * @param {Node} node\n   */\n  static _collapse(node) {\n    if (typeof node === \"string\") return;\n    if (!node.children) return;\n\n    if (node.children.every(el => typeof el === \"string\")) {\n      // node.text = node.children.join(\"\");\n      // delete node.children;\n      node.children = [node.children.join(\"\")];\n    } else {\n      node.children.forEach((child) => {\n        TokenTree._collapse(child);\n      });\n    }\n  }\n}\n\n/**\n  Currently this is all private API, but this is the minimal API necessary\n  that an Emitter must implement to fully support the parser.\n\n  Minimal interface:\n\n  - addKeyword(text, kind)\n  - addText(text)\n  - addSublanguage(emitter, subLanguageName)\n  - finalize()\n  - openNode(kind)\n  - closeNode()\n  - closeAllNodes()\n  - toHTML()\n\n*/\n\n/**\n * @implements {Emitter}\n */\nclass TokenTreeEmitter extends TokenTree {\n  /**\n   * @param {*} options\n   */\n  constructor(options) {\n    super();\n    this.options = options;\n  }\n\n  /**\n   * @param {string} text\n   * @param {string} kind\n   */\n  addKeyword(text, kind) {\n    if (text === \"\") { return; }\n\n    this.openNode(kind);\n    this.addText(text);\n    this.closeNode();\n  }\n\n  /**\n   * @param {string} text\n   */\n  addText(text) {\n    if (text === \"\") { return; }\n\n    this.add(text);\n  }\n\n  /**\n   * @param {Emitter & {root: DataNode}} emitter\n   * @param {string} name\n   */\n  addSublanguage(emitter, name) {\n    /** @type DataNode */\n    const node = emitter.root;\n    node.kind = name;\n    node.sublanguage = true;\n    this.add(node);\n  }\n\n  toHTML() {\n    const renderer = new HTMLRenderer(this, this.options);\n    return renderer.value();\n  }\n\n  finalize() {\n    return true;\n  }\n}\n\n/**\n * @param {string} value\n * @returns {RegExp}\n * */\nfunction escape(value) {\n  return new RegExp(value.replace(/[-/\\\\^$*+?.()|[\\]{}]/g, '\\\\$&'), 'm');\n}\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction source(re) {\n  if (!re) return null;\n  if (typeof re === \"string\") return re;\n\n  return re.source;\n}\n\n/**\n * @param {...(RegExp | string) } args\n * @returns {string}\n */\nfunction concat(...args) {\n  const joined = args.map((x) => source(x)).join(\"\");\n  return joined;\n}\n\n/**\n * Any of the passed expresssions may match\n *\n * Creates a huge this | this | that | that match\n * @param {(RegExp | string)[] } args\n * @returns {string}\n */\nfunction either(...args) {\n  const joined = '(' + args.map((x) => source(x)).join(\"|\") + \")\";\n  return joined;\n}\n\n/**\n * @param {RegExp} re\n * @returns {number}\n */\nfunction countMatchGroups(re) {\n  return (new RegExp(re.toString() + '|')).exec('').length - 1;\n}\n\n/**\n * Does lexeme start with a regular expression match at the beginning\n * @param {RegExp} re\n * @param {string} lexeme\n */\nfunction startsWith(re, lexeme) {\n  const match = re && re.exec(lexeme);\n  return match && match.index === 0;\n}\n\n// BACKREF_RE matches an open parenthesis or backreference. To avoid\n// an incorrect parse, it additionally matches the following:\n// - [...] elements, where the meaning of parentheses and escapes change\n// - other escape sequences, so we do not misparse escape sequences as\n//   interesting elements\n// - non-matching or lookahead parentheses, which do not capture. These\n//   follow the '(' with a '?'.\nconst BACKREF_RE = /\\[(?:[^\\\\\\]]|\\\\.)*\\]|\\(\\??|\\\\([1-9][0-9]*)|\\\\./;\n\n// join logically computes regexps.join(separator), but fixes the\n// backreferences so they continue to match.\n// it also places each individual regular expression into it's own\n// match group, keeping track of the sequencing of those match groups\n// is currently an exercise for the caller. :-)\n/**\n * @param {(string | RegExp)[]} regexps\n * @param {string} separator\n * @returns {string}\n */\nfunction join(regexps, separator = \"|\") {\n  let numCaptures = 0;\n\n  return regexps.map((regex) => {\n    numCaptures += 1;\n    const offset = numCaptures;\n    let re = source(regex);\n    let out = '';\n\n    while (re.length > 0) {\n      const match = BACKREF_RE.exec(re);\n      if (!match) {\n        out += re;\n        break;\n      }\n      out += re.substring(0, match.index);\n      re = re.substring(match.index + match[0].length);\n      if (match[0][0] === '\\\\' && match[1]) {\n        // Adjust the backreference.\n        out += '\\\\' + String(Number(match[1]) + offset);\n      } else {\n        out += match[0];\n        if (match[0] === '(') {\n          numCaptures++;\n        }\n      }\n    }\n    return out;\n  }).map(re => `(${re})`).join(separator);\n}\n\n// Common regexps\nconst MATCH_NOTHING_RE = /\\b\\B/;\nconst IDENT_RE = '[a-zA-Z]\\\\w*';\nconst UNDERSCORE_IDENT_RE = '[a-zA-Z_]\\\\w*';\nconst NUMBER_RE = '\\\\b\\\\d+(\\\\.\\\\d+)?';\nconst C_NUMBER_RE = '(-?)(\\\\b0[xX][a-fA-F0-9]+|(\\\\b\\\\d+(\\\\.\\\\d*)?|\\\\.\\\\d+)([eE][-+]?\\\\d+)?)'; // 0x..., 0..., decimal, float\nconst BINARY_NUMBER_RE = '\\\\b(0b[01]+)'; // 0b...\nconst RE_STARTERS_RE = '!|!=|!==|%|%=|&|&&|&=|\\\\*|\\\\*=|\\\\+|\\\\+=|,|-|-=|/=|/|:|;|<<|<<=|<=|<|===|==|=|>>>=|>>=|>=|>>>|>>|>|\\\\?|\\\\[|\\\\{|\\\\(|\\\\^|\\\\^=|\\\\||\\\\|=|\\\\|\\\\||~';\n\n/**\n* @param { Partial<Mode> & {binary?: string | RegExp} } opts\n*/\nconst SHEBANG = (opts = {}) => {\n  const beginShebang = /^#![ ]*\\//;\n  if (opts.binary) {\n    opts.begin = concat(\n      beginShebang,\n      /.*\\b/,\n      opts.binary,\n      /\\b.*/);\n  }\n  return inherit({\n    className: 'meta',\n    begin: beginShebang,\n    end: /$/,\n    relevance: 0,\n    /** @type {ModeCallback} */\n    \"on:begin\": (m, resp) => {\n      if (m.index !== 0) resp.ignoreMatch();\n    }\n  }, opts);\n};\n\n// Common modes\nconst BACKSLASH_ESCAPE = {\n  begin: '\\\\\\\\[\\\\s\\\\S]', relevance: 0\n};\nconst APOS_STRING_MODE = {\n  className: 'string',\n  begin: '\\'',\n  end: '\\'',\n  illegal: '\\\\n',\n  contains: [BACKSLASH_ESCAPE]\n};\nconst QUOTE_STRING_MODE = {\n  className: 'string',\n  begin: '\"',\n  end: '\"',\n  illegal: '\\\\n',\n  contains: [BACKSLASH_ESCAPE]\n};\nconst PHRASAL_WORDS_MODE = {\n  begin: /\\b(a|an|the|are|I'm|isn't|don't|doesn't|won't|but|just|should|pretty|simply|enough|gonna|going|wtf|so|such|will|you|your|they|like|more)\\b/\n};\n/**\n * Creates a comment mode\n *\n * @param {string | RegExp} begin\n * @param {string | RegExp} end\n * @param {Mode | {}} [modeOptions]\n * @returns {Partial<Mode>}\n */\nconst COMMENT = function(begin, end, modeOptions = {}) {\n  const mode = inherit(\n    {\n      className: 'comment',\n      begin,\n      end,\n      contains: []\n    },\n    modeOptions\n  );\n  mode.contains.push(PHRASAL_WORDS_MODE);\n  mode.contains.push({\n    className: 'doctag',\n    begin: '(?:TODO|FIXME|NOTE|BUG|OPTIMIZE|HACK|XXX):',\n    relevance: 0\n  });\n  return mode;\n};\nconst C_LINE_COMMENT_MODE = COMMENT('//', '$');\nconst C_BLOCK_COMMENT_MODE = COMMENT('/\\\\*', '\\\\*/');\nconst HASH_COMMENT_MODE = COMMENT('#', '$');\nconst NUMBER_MODE = {\n  className: 'number',\n  begin: NUMBER_RE,\n  relevance: 0\n};\nconst C_NUMBER_MODE = {\n  className: 'number',\n  begin: C_NUMBER_RE,\n  relevance: 0\n};\nconst BINARY_NUMBER_MODE = {\n  className: 'number',\n  begin: BINARY_NUMBER_RE,\n  relevance: 0\n};\nconst CSS_NUMBER_MODE = {\n  className: 'number',\n  begin: NUMBER_RE + '(' +\n    '%|em|ex|ch|rem' +\n    '|vw|vh|vmin|vmax' +\n    '|cm|mm|in|pt|pc|px' +\n    '|deg|grad|rad|turn' +\n    '|s|ms' +\n    '|Hz|kHz' +\n    '|dpi|dpcm|dppx' +\n    ')?',\n  relevance: 0\n};\nconst REGEXP_MODE = {\n  // this outer rule makes sure we actually have a WHOLE regex and not simply\n  // an expression such as:\n  //\n  //     3 / something\n  //\n  // (which will then blow up when regex's `illegal` sees the newline)\n  begin: /(?=\\/[^/\\n]*\\/)/,\n  contains: [{\n    className: 'regexp',\n    begin: /\\//,\n    end: /\\/[gimuy]*/,\n    illegal: /\\n/,\n    contains: [\n      BACKSLASH_ESCAPE,\n      {\n        begin: /\\[/,\n        end: /\\]/,\n        relevance: 0,\n        contains: [BACKSLASH_ESCAPE]\n      }\n    ]\n  }]\n};\nconst TITLE_MODE = {\n  className: 'title',\n  begin: IDENT_RE,\n  relevance: 0\n};\nconst UNDERSCORE_TITLE_MODE = {\n  className: 'title',\n  begin: UNDERSCORE_IDENT_RE,\n  relevance: 0\n};\nconst METHOD_GUARD = {\n  // excludes method names from keyword processing\n  begin: '\\\\.\\\\s*' + UNDERSCORE_IDENT_RE,\n  relevance: 0\n};\n\n/**\n * Adds end same as begin mechanics to a mode\n *\n * Your mode must include at least a single () match group as that first match\n * group is what is used for comparison\n * @param {Partial<Mode>} mode\n */\nconst END_SAME_AS_BEGIN = function(mode) {\n  return Object.assign(mode,\n    {\n      /** @type {ModeCallback} */\n      'on:begin': (m, resp) => { resp.data._beginMatch = m[1]; },\n      /** @type {ModeCallback} */\n      'on:end': (m, resp) => { if (resp.data._beginMatch !== m[1]) resp.ignoreMatch(); }\n    });\n};\n\nvar MODES = /*#__PURE__*/Object.freeze({\n    __proto__: null,\n    MATCH_NOTHING_RE: MATCH_NOTHING_RE,\n    IDENT_RE: IDENT_RE,\n    UNDERSCORE_IDENT_RE: UNDERSCORE_IDENT_RE,\n    NUMBER_RE: NUMBER_RE,\n    C_NUMBER_RE: C_NUMBER_RE,\n    BINARY_NUMBER_RE: BINARY_NUMBER_RE,\n    RE_STARTERS_RE: RE_STARTERS_RE,\n    SHEBANG: SHEBANG,\n    BACKSLASH_ESCAPE: BACKSLASH_ESCAPE,\n    APOS_STRING_MODE: APOS_STRING_MODE,\n    QUOTE_STRING_MODE: QUOTE_STRING_MODE,\n    PHRASAL_WORDS_MODE: PHRASAL_WORDS_MODE,\n    COMMENT: COMMENT,\n    C_LINE_COMMENT_MODE: C_LINE_COMMENT_MODE,\n    C_BLOCK_COMMENT_MODE: C_BLOCK_COMMENT_MODE,\n    HASH_COMMENT_MODE: HASH_COMMENT_MODE,\n    NUMBER_MODE: NUMBER_MODE,\n    C_NUMBER_MODE: C_NUMBER_MODE,\n    BINARY_NUMBER_MODE: BINARY_NUMBER_MODE,\n    CSS_NUMBER_MODE: CSS_NUMBER_MODE,\n    REGEXP_MODE: REGEXP_MODE,\n    TITLE_MODE: TITLE_MODE,\n    UNDERSCORE_TITLE_MODE: UNDERSCORE_TITLE_MODE,\n    METHOD_GUARD: METHOD_GUARD,\n    END_SAME_AS_BEGIN: END_SAME_AS_BEGIN\n});\n\n// Grammar extensions / plugins\n// See: https://github.com/highlightjs/highlight.js/issues/2833\n\n// Grammar extensions allow \"syntactic sugar\" to be added to the grammar modes\n// without requiring any underlying changes to the compiler internals.\n\n// `compileMatch` being the perfect small example of now allowing a grammar\n// author to write `match` when they desire to match a single expression rather\n// than being forced to use `begin`.  The extension then just moves `match` into\n// `begin` when it runs.  Ie, no features have been added, but we've just made\n// the experience of writing (and reading grammars) a little bit nicer.\n\n// ------\n\n// TODO: We need negative look-behind support to do this properly\n/**\n * Skip a match if it has a preceding dot\n *\n * This is used for `beginKeywords` to prevent matching expressions such as\n * `bob.keyword.do()`. The mode compiler automatically wires this up as a\n * special _internal_ 'on:begin' callback for modes with `beginKeywords`\n * @param {RegExpMatchArray} match\n * @param {CallbackResponse} response\n */\nfunction skipIfhasPrecedingDot(match, response) {\n  const before = match.input[match.index - 1];\n  if (before === \".\") {\n    response.ignoreMatch();\n  }\n}\n\n\n/**\n * `beginKeywords` syntactic sugar\n * @type {CompilerExt}\n */\nfunction beginKeywords(mode, parent) {\n  if (!parent) return;\n  if (!mode.beginKeywords) return;\n\n  // for languages with keywords that include non-word characters checking for\n  // a word boundary is not sufficient, so instead we check for a word boundary\n  // or whitespace - this does no harm in any case since our keyword engine\n  // doesn't allow spaces in keywords anyways and we still check for the boundary\n  // first\n  mode.begin = '\\\\b(' + mode.beginKeywords.split(' ').join('|') + ')(?!\\\\.)(?=\\\\b|\\\\s)';\n  mode.__beforeBegin = skipIfhasPrecedingDot;\n  mode.keywords = mode.keywords || mode.beginKeywords;\n  delete mode.beginKeywords;\n\n  // prevents double relevance, the keywords themselves provide\n  // relevance, the mode doesn't need to double it\n  // eslint-disable-next-line no-undefined\n  if (mode.relevance === undefined) mode.relevance = 0;\n}\n\n/**\n * Allow `illegal` to contain an array of illegal values\n * @type {CompilerExt}\n */\nfunction compileIllegal(mode, _parent) {\n  if (!Array.isArray(mode.illegal)) return;\n\n  mode.illegal = either(...mode.illegal);\n}\n\n/**\n * `match` to match a single expression for readability\n * @type {CompilerExt}\n */\nfunction compileMatch(mode, _parent) {\n  if (!mode.match) return;\n  if (mode.begin || mode.end) throw new Error(\"begin & end are not supported with match\");\n\n  mode.begin = mode.match;\n  delete mode.match;\n}\n\n/**\n * provides the default 1 relevance to all modes\n * @type {CompilerExt}\n */\nfunction compileRelevance(mode, _parent) {\n  // eslint-disable-next-line no-undefined\n  if (mode.relevance === undefined) mode.relevance = 1;\n}\n\n// keywords that should have no default relevance value\nconst COMMON_KEYWORDS = [\n  'of',\n  'and',\n  'for',\n  'in',\n  'not',\n  'or',\n  'if',\n  'then',\n  'parent', // common variable name\n  'list', // common variable name\n  'value' // common variable name\n];\n\nconst DEFAULT_KEYWORD_CLASSNAME = \"keyword\";\n\n/**\n * Given raw keywords from a language definition, compile them.\n *\n * @param {string | Record<string,string|string[]> | Array<string>} rawKeywords\n * @param {boolean} caseInsensitive\n */\nfunction compileKeywords(rawKeywords, caseInsensitive, className = DEFAULT_KEYWORD_CLASSNAME) {\n  /** @type KeywordDict */\n  const compiledKeywords = {};\n\n  // input can be a string of keywords, an array of keywords, or a object with\n  // named keys representing className (which can then point to a string or array)\n  if (typeof rawKeywords === 'string') {\n    compileList(className, rawKeywords.split(\" \"));\n  } else if (Array.isArray(rawKeywords)) {\n    compileList(className, rawKeywords);\n  } else {\n    Object.keys(rawKeywords).forEach(function(className) {\n      // collapse all our objects back into the parent object\n      Object.assign(\n        compiledKeywords,\n        compileKeywords(rawKeywords[className], caseInsensitive, className)\n      );\n    });\n  }\n  return compiledKeywords;\n\n  // ---\n\n  /**\n   * Compiles an individual list of keywords\n   *\n   * Ex: \"for if when while|5\"\n   *\n   * @param {string} className\n   * @param {Array<string>} keywordList\n   */\n  function compileList(className, keywordList) {\n    if (caseInsensitive) {\n      keywordList = keywordList.map(x => x.toLowerCase());\n    }\n    keywordList.forEach(function(keyword) {\n      const pair = keyword.split('|');\n      compiledKeywords[pair[0]] = [className, scoreForKeyword(pair[0], pair[1])];\n    });\n  }\n}\n\n/**\n * Returns the proper score for a given keyword\n *\n * Also takes into account comment keywords, which will be scored 0 UNLESS\n * another score has been manually assigned.\n * @param {string} keyword\n * @param {string} [providedScore]\n */\nfunction scoreForKeyword(keyword, providedScore) {\n  // manual scores always win over common keywords\n  // so you can force a score of 1 if you really insist\n  if (providedScore) {\n    return Number(providedScore);\n  }\n\n  return commonKeyword(keyword) ? 0 : 1;\n}\n\n/**\n * Determines if a given keyword is common or not\n *\n * @param {string} keyword */\nfunction commonKeyword(keyword) {\n  return COMMON_KEYWORDS.includes(keyword.toLowerCase());\n}\n\n// compilation\n\n/**\n * Compiles a language definition result\n *\n * Given the raw result of a language definition (Language), compiles this so\n * that it is ready for highlighting code.\n * @param {Language} language\n * @param {{plugins: HLJSPlugin[]}} opts\n * @returns {CompiledLanguage}\n */\nfunction compileLanguage(language, { plugins }) {\n  /**\n   * Builds a regex with the case sensativility of the current language\n   *\n   * @param {RegExp | string} value\n   * @param {boolean} [global]\n   */\n  function langRe(value, global) {\n    return new RegExp(\n      source(value),\n      'm' + (language.case_insensitive ? 'i' : '') + (global ? 'g' : '')\n    );\n  }\n\n  /**\n    Stores multiple regular expressions and allows you to quickly search for\n    them all in a string simultaneously - returning the first match.  It does\n    this by creating a huge (a|b|c) regex - each individual item wrapped with ()\n    and joined by `|` - using match groups to track position.  When a match is\n    found checking which position in the array has content allows us to figure\n    out which of the original regexes / match groups triggered the match.\n\n    The match object itself (the result of `Regex.exec`) is returned but also\n    enhanced by merging in any meta-data that was registered with the regex.\n    This is how we keep track of which mode matched, and what type of rule\n    (`illegal`, `begin`, end, etc).\n  */\n  class MultiRegex {\n    constructor() {\n      this.matchIndexes = {};\n      // @ts-ignore\n      this.regexes = [];\n      this.matchAt = 1;\n      this.position = 0;\n    }\n\n    // @ts-ignore\n    addRule(re, opts) {\n      opts.position = this.position++;\n      // @ts-ignore\n      this.matchIndexes[this.matchAt] = opts;\n      this.regexes.push([opts, re]);\n      this.matchAt += countMatchGroups(re) + 1;\n    }\n\n    compile() {\n      if (this.regexes.length === 0) {\n        // avoids the need to check length every time exec is called\n        // @ts-ignore\n        this.exec = () => null;\n      }\n      const terminators = this.regexes.map(el => el[1]);\n      this.matcherRe = langRe(join(terminators), true);\n      this.lastIndex = 0;\n    }\n\n    /** @param {string} s */\n    exec(s) {\n      this.matcherRe.lastIndex = this.lastIndex;\n      const match = this.matcherRe.exec(s);\n      if (!match) { return null; }\n\n      // eslint-disable-next-line no-undefined\n      const i = match.findIndex((el, i) => i > 0 && el !== undefined);\n      // @ts-ignore\n      const matchData = this.matchIndexes[i];\n      // trim off any earlier non-relevant match groups (ie, the other regex\n      // match groups that make up the multi-matcher)\n      match.splice(0, i);\n\n      return Object.assign(match, matchData);\n    }\n  }\n\n  /*\n    Created to solve the key deficiently with MultiRegex - there is no way to\n    test for multiple matches at a single location.  Why would we need to do\n    that?  In the future a more dynamic engine will allow certain matches to be\n    ignored.  An example: if we matched say the 3rd regex in a large group but\n    decided to ignore it - we'd need to started testing again at the 4th\n    regex... but MultiRegex itself gives us no real way to do that.\n\n    So what this class creates MultiRegexs on the fly for whatever search\n    position they are needed.\n\n    NOTE: These additional MultiRegex objects are created dynamically.  For most\n    grammars most of the time we will never actually need anything more than the\n    first MultiRegex - so this shouldn't have too much overhead.\n\n    Say this is our search group, and we match regex3, but wish to ignore it.\n\n      regex1 | regex2 | regex3 | regex4 | regex5    ' ie, startAt = 0\n\n    What we need is a new MultiRegex that only includes the remaining\n    possibilities:\n\n      regex4 | regex5                               ' ie, startAt = 3\n\n    This class wraps all that complexity up in a simple API... `startAt` decides\n    where in the array of expressions to start doing the matching. It\n    auto-increments, so if a match is found at position 2, then startAt will be\n    set to 3.  If the end is reached startAt will return to 0.\n\n    MOST of the time the parser will be setting startAt manually to 0.\n  */\n  class ResumableMultiRegex {\n    constructor() {\n      // @ts-ignore\n      this.rules = [];\n      // @ts-ignore\n      this.multiRegexes = [];\n      this.count = 0;\n\n      this.lastIndex = 0;\n      this.regexIndex = 0;\n    }\n\n    // @ts-ignore\n    getMatcher(index) {\n      if (this.multiRegexes[index]) return this.multiRegexes[index];\n\n      const matcher = new MultiRegex();\n      this.rules.slice(index).forEach(([re, opts]) => matcher.addRule(re, opts));\n      matcher.compile();\n      this.multiRegexes[index] = matcher;\n      return matcher;\n    }\n\n    resumingScanAtSamePosition() {\n      return this.regexIndex !== 0;\n    }\n\n    considerAll() {\n      this.regexIndex = 0;\n    }\n\n    // @ts-ignore\n    addRule(re, opts) {\n      this.rules.push([re, opts]);\n      if (opts.type === \"begin\") this.count++;\n    }\n\n    /** @param {string} s */\n    exec(s) {\n      const m = this.getMatcher(this.regexIndex);\n      m.lastIndex = this.lastIndex;\n      let result = m.exec(s);\n\n      // The following is because we have no easy way to say \"resume scanning at the\n      // existing position but also skip the current rule ONLY\". What happens is\n      // all prior rules are also skipped which can result in matching the wrong\n      // thing. Example of matching \"booger\":\n\n      // our matcher is [string, \"booger\", number]\n      //\n      // ....booger....\n\n      // if \"booger\" is ignored then we'd really need a regex to scan from the\n      // SAME position for only: [string, number] but ignoring \"booger\" (if it\n      // was the first match), a simple resume would scan ahead who knows how\n      // far looking only for \"number\", ignoring potential string matches (or\n      // future \"booger\" matches that might be valid.)\n\n      // So what we do: We execute two matchers, one resuming at the same\n      // position, but the second full matcher starting at the position after:\n\n      //     /--- resume first regex match here (for [number])\n      //     |/---- full match here for [string, \"booger\", number]\n      //     vv\n      // ....booger....\n\n      // Which ever results in a match first is then used. So this 3-4 step\n      // process essentially allows us to say \"match at this position, excluding\n      // a prior rule that was ignored\".\n      //\n      // 1. Match \"booger\" first, ignore. Also proves that [string] does non match.\n      // 2. Resume matching for [number]\n      // 3. Match at index + 1 for [string, \"booger\", number]\n      // 4. If #2 and #3 result in matches, which came first?\n      if (this.resumingScanAtSamePosition()) {\n        if (result && result.index === this.lastIndex) ; else { // use the second matcher result\n          const m2 = this.getMatcher(0);\n          m2.lastIndex = this.lastIndex + 1;\n          result = m2.exec(s);\n        }\n      }\n\n      if (result) {\n        this.regexIndex += result.position + 1;\n        if (this.regexIndex === this.count) {\n          // wrap-around to considering all matches again\n          this.considerAll();\n        }\n      }\n\n      return result;\n    }\n  }\n\n  /**\n   * Given a mode, builds a huge ResumableMultiRegex that can be used to walk\n   * the content and find matches.\n   *\n   * @param {CompiledMode} mode\n   * @returns {ResumableMultiRegex}\n   */\n  function buildModeRegex(mode) {\n    const mm = new ResumableMultiRegex();\n\n    mode.contains.forEach(term => mm.addRule(term.begin, { rule: term, type: \"begin\" }));\n\n    if (mode.terminatorEnd) {\n      mm.addRule(mode.terminatorEnd, { type: \"end\" });\n    }\n    if (mode.illegal) {\n      mm.addRule(mode.illegal, { type: \"illegal\" });\n    }\n\n    return mm;\n  }\n\n  /** skip vs abort vs ignore\n   *\n   * @skip   - The mode is still entered and exited normally (and contains rules apply),\n   *           but all content is held and added to the parent buffer rather than being\n   *           output when the mode ends.  Mostly used with `sublanguage` to build up\n   *           a single large buffer than can be parsed by sublanguage.\n   *\n   *             - The mode begin ands ends normally.\n   *             - Content matched is added to the parent mode buffer.\n   *             - The parser cursor is moved forward normally.\n   *\n   * @abort  - A hack placeholder until we have ignore.  Aborts the mode (as if it\n   *           never matched) but DOES NOT continue to match subsequent `contains`\n   *           modes.  Abort is bad/suboptimal because it can result in modes\n   *           farther down not getting applied because an earlier rule eats the\n   *           content but then aborts.\n   *\n   *             - The mode does not begin.\n   *             - Content matched by `begin` is added to the mode buffer.\n   *             - The parser cursor is moved forward accordingly.\n   *\n   * @ignore - Ignores the mode (as if it never matched) and continues to match any\n   *           subsequent `contains` modes.  Ignore isn't technically possible with\n   *           the current parser implementation.\n   *\n   *             - The mode does not begin.\n   *             - Content matched by `begin` is ignored.\n   *             - The parser cursor is not moved forward.\n   */\n\n  /**\n   * Compiles an individual mode\n   *\n   * This can raise an error if the mode contains certain detectable known logic\n   * issues.\n   * @param {Mode} mode\n   * @param {CompiledMode | null} [parent]\n   * @returns {CompiledMode | never}\n   */\n  function compileMode(mode, parent) {\n    const cmode = /** @type CompiledMode */ (mode);\n    if (mode.isCompiled) return cmode;\n\n    [\n      // do this early so compiler extensions generally don't have to worry about\n      // the distinction between match/begin\n      compileMatch\n    ].forEach(ext => ext(mode, parent));\n\n    language.compilerExtensions.forEach(ext => ext(mode, parent));\n\n    // __beforeBegin is considered private API, internal use only\n    mode.__beforeBegin = null;\n\n    [\n      beginKeywords,\n      // do this later so compiler extensions that come earlier have access to the\n      // raw array if they wanted to perhaps manipulate it, etc.\n      compileIllegal,\n      // default to 1 relevance if not specified\n      compileRelevance\n    ].forEach(ext => ext(mode, parent));\n\n    mode.isCompiled = true;\n\n    let keywordPattern = null;\n    if (typeof mode.keywords === \"object\") {\n      keywordPattern = mode.keywords.$pattern;\n      delete mode.keywords.$pattern;\n    }\n\n    if (mode.keywords) {\n      mode.keywords = compileKeywords(mode.keywords, language.case_insensitive);\n    }\n\n    // both are not allowed\n    if (mode.lexemes && keywordPattern) {\n      throw new Error(\"ERR: Prefer `keywords.$pattern` to `mode.lexemes`, BOTH are not allowed. (see mode reference) \");\n    }\n\n    // `mode.lexemes` was the old standard before we added and now recommend\n    // using `keywords.$pattern` to pass the keyword pattern\n    keywordPattern = keywordPattern || mode.lexemes || /\\w+/;\n    cmode.keywordPatternRe = langRe(keywordPattern, true);\n\n    if (parent) {\n      if (!mode.begin) mode.begin = /\\B|\\b/;\n      cmode.beginRe = langRe(mode.begin);\n      if (mode.endSameAsBegin) mode.end = mode.begin;\n      if (!mode.end && !mode.endsWithParent) mode.end = /\\B|\\b/;\n      if (mode.end) cmode.endRe = langRe(mode.end);\n      cmode.terminatorEnd = source(mode.end) || '';\n      if (mode.endsWithParent && parent.terminatorEnd) {\n        cmode.terminatorEnd += (mode.end ? '|' : '') + parent.terminatorEnd;\n      }\n    }\n    if (mode.illegal) cmode.illegalRe = langRe(/** @type {RegExp | string} */ (mode.illegal));\n    if (!mode.contains) mode.contains = [];\n\n    mode.contains = [].concat(...mode.contains.map(function(c) {\n      return expandOrCloneMode(c === 'self' ? mode : c);\n    }));\n    mode.contains.forEach(function(c) { compileMode(/** @type Mode */ (c), cmode); });\n\n    if (mode.starts) {\n      compileMode(mode.starts, parent);\n    }\n\n    cmode.matcher = buildModeRegex(cmode);\n    return cmode;\n  }\n\n  if (!language.compilerExtensions) language.compilerExtensions = [];\n\n  // self is not valid at the top-level\n  if (language.contains && language.contains.includes('self')) {\n    throw new Error(\"ERR: contains `self` is not supported at the top-level of a language.  See documentation.\");\n  }\n\n  // we need a null object, which inherit will guarantee\n  language.classNameAliases = inherit(language.classNameAliases || {});\n\n  return compileMode(/** @type Mode */ (language));\n}\n\n/**\n * Determines if a mode has a dependency on it's parent or not\n *\n * If a mode does have a parent dependency then often we need to clone it if\n * it's used in multiple places so that each copy points to the correct parent,\n * where-as modes without a parent can often safely be re-used at the bottom of\n * a mode chain.\n *\n * @param {Mode | null} mode\n * @returns {boolean} - is there a dependency on the parent?\n * */\nfunction dependencyOnParent(mode) {\n  if (!mode) return false;\n\n  return mode.endsWithParent || dependencyOnParent(mode.starts);\n}\n\n/**\n * Expands a mode or clones it if necessary\n *\n * This is necessary for modes with parental dependenceis (see notes on\n * `dependencyOnParent`) and for nodes that have `variants` - which must then be\n * exploded into their own individual modes at compile time.\n *\n * @param {Mode} mode\n * @returns {Mode | Mode[]}\n * */\nfunction expandOrCloneMode(mode) {\n  if (mode.variants && !mode.cachedVariants) {\n    mode.cachedVariants = mode.variants.map(function(variant) {\n      return inherit(mode, { variants: null }, variant);\n    });\n  }\n\n  // EXPAND\n  // if we have variants then essentially \"replace\" the mode with the variants\n  // this happens in compileMode, where this function is called from\n  if (mode.cachedVariants) {\n    return mode.cachedVariants;\n  }\n\n  // CLONE\n  // if we have dependencies on parents then we need a unique\n  // instance of ourselves, so we can be reused with many\n  // different parents without issue\n  if (dependencyOnParent(mode)) {\n    return inherit(mode, { starts: mode.starts ? inherit(mode.starts) : null });\n  }\n\n  if (Object.isFrozen(mode)) {\n    return inherit(mode);\n  }\n\n  // no special dependency issues, just return ourselves\n  return mode;\n}\n\nvar version = \"10.7.3\";\n\n// @ts-nocheck\n\nfunction hasValueOrEmptyAttribute(value) {\n  return Boolean(value || value === \"\");\n}\n\nfunction BuildVuePlugin(hljs) {\n  const Component = {\n    props: [\"language\", \"code\", \"autodetect\"],\n    data: function() {\n      return {\n        detectedLanguage: \"\",\n        unknownLanguage: false\n      };\n    },\n    computed: {\n      className() {\n        if (this.unknownLanguage) return \"\";\n\n        return \"hljs \" + this.detectedLanguage;\n      },\n      highlighted() {\n        // no idea what language to use, return raw code\n        if (!this.autoDetect && !hljs.getLanguage(this.language)) {\n          console.warn(`The language \"${this.language}\" you specified could not be found.`);\n          this.unknownLanguage = true;\n          return escapeHTML(this.code);\n        }\n\n        let result = {};\n        if (this.autoDetect) {\n          result = hljs.highlightAuto(this.code);\n          this.detectedLanguage = result.language;\n        } else {\n          result = hljs.highlight(this.language, this.code, this.ignoreIllegals);\n          this.detectedLanguage = this.language;\n        }\n        return result.value;\n      },\n      autoDetect() {\n        return !this.language || hasValueOrEmptyAttribute(this.autodetect);\n      },\n      ignoreIllegals() {\n        return true;\n      }\n    },\n    // this avoids needing to use a whole Vue compilation pipeline just\n    // to build Highlight.js\n    render(createElement) {\n      return createElement(\"pre\", {}, [\n        createElement(\"code\", {\n          class: this.className,\n          domProps: { innerHTML: this.highlighted }\n        })\n      ]);\n    }\n    // template: `<pre><code :class=\"className\" v-html=\"highlighted\"></code></pre>`\n  };\n\n  const VuePlugin = {\n    install(Vue) {\n      Vue.component('highlightjs', Component);\n    }\n  };\n\n  return { Component, VuePlugin };\n}\n\n/* plugin itself */\n\n/** @type {HLJSPlugin} */\nconst mergeHTMLPlugin = {\n  \"after:highlightElement\": ({ el, result, text }) => {\n    const originalStream = nodeStream(el);\n    if (!originalStream.length) return;\n\n    const resultNode = document.createElement('div');\n    resultNode.innerHTML = result.value;\n    result.value = mergeStreams(originalStream, nodeStream(resultNode), text);\n  }\n};\n\n/* Stream merging support functions */\n\n/**\n * @typedef Event\n * @property {'start'|'stop'} event\n * @property {number} offset\n * @property {Node} node\n */\n\n/**\n * @param {Node} node\n */\nfunction tag(node) {\n  return node.nodeName.toLowerCase();\n}\n\n/**\n * @param {Node} node\n */\nfunction nodeStream(node) {\n  /** @type Event[] */\n  const result = [];\n  (function _nodeStream(node, offset) {\n    for (let child = node.firstChild; child; child = child.nextSibling) {\n      if (child.nodeType === 3) {\n        offset += child.nodeValue.length;\n      } else if (child.nodeType === 1) {\n        result.push({\n          event: 'start',\n          offset: offset,\n          node: child\n        });\n        offset = _nodeStream(child, offset);\n        // Prevent void elements from having an end tag that would actually\n        // double them in the output. There are more void elements in HTML\n        // but we list only those realistically expected in code display.\n        if (!tag(child).match(/br|hr|img|input/)) {\n          result.push({\n            event: 'stop',\n            offset: offset,\n            node: child\n          });\n        }\n      }\n    }\n    return offset;\n  })(node, 0);\n  return result;\n}\n\n/**\n * @param {any} original - the original stream\n * @param {any} highlighted - stream of the highlighted source\n * @param {string} value - the original source itself\n */\nfunction mergeStreams(original, highlighted, value) {\n  let processed = 0;\n  let result = '';\n  const nodeStack = [];\n\n  function selectStream() {\n    if (!original.length || !highlighted.length) {\n      return original.length ? original : highlighted;\n    }\n    if (original[0].offset !== highlighted[0].offset) {\n      return (original[0].offset < highlighted[0].offset) ? original : highlighted;\n    }\n\n    /*\n    To avoid starting the stream just before it should stop the order is\n    ensured that original always starts first and closes last:\n\n    if (event1 == 'start' && event2 == 'start')\n      return original;\n    if (event1 == 'start' && event2 == 'stop')\n      return highlighted;\n    if (event1 == 'stop' && event2 == 'start')\n      return original;\n    if (event1 == 'stop' && event2 == 'stop')\n      return highlighted;\n\n    ... which is collapsed to:\n    */\n    return highlighted[0].event === 'start' ? original : highlighted;\n  }\n\n  /**\n   * @param {Node} node\n   */\n  function open(node) {\n    /** @param {Attr} attr */\n    function attributeString(attr) {\n      return ' ' + attr.nodeName + '=\"' + escapeHTML(attr.value) + '\"';\n    }\n    // @ts-ignore\n    result += '<' + tag(node) + [].map.call(node.attributes, attributeString).join('') + '>';\n  }\n\n  /**\n   * @param {Node} node\n   */\n  function close(node) {\n    result += '</' + tag(node) + '>';\n  }\n\n  /**\n   * @param {Event} event\n   */\n  function render(event) {\n    (event.event === 'start' ? open : close)(event.node);\n  }\n\n  while (original.length || highlighted.length) {\n    let stream = selectStream();\n    result += escapeHTML(value.substring(processed, stream[0].offset));\n    processed = stream[0].offset;\n    if (stream === original) {\n      /*\n      On any opening or closing tag of the original markup we first close\n      the entire highlighted node stack, then render the original tag along\n      with all the following original tags at the same offset and then\n      reopen all the tags on the highlighted stack.\n      */\n      nodeStack.reverse().forEach(close);\n      do {\n        render(stream.splice(0, 1)[0]);\n        stream = selectStream();\n      } while (stream === original && stream.length && stream[0].offset === processed);\n      nodeStack.reverse().forEach(open);\n    } else {\n      if (stream[0].event === 'start') {\n        nodeStack.push(stream[0].node);\n      } else {\n        nodeStack.pop();\n      }\n      render(stream.splice(0, 1)[0]);\n    }\n  }\n  return result + escapeHTML(value.substr(processed));\n}\n\n/*\n\nFor the reasoning behind this please see:\nhttps://github.com/highlightjs/highlight.js/issues/2880#issuecomment-747275419\n\n*/\n\n/**\n * @type {Record<string, boolean>}\n */\nconst seenDeprecations = {};\n\n/**\n * @param {string} message\n */\nconst error = (message) => {\n  console.error(message);\n};\n\n/**\n * @param {string} message\n * @param {any} args\n */\nconst warn = (message, ...args) => {\n  console.log(`WARN: ${message}`, ...args);\n};\n\n/**\n * @param {string} version\n * @param {string} message\n */\nconst deprecated = (version, message) => {\n  if (seenDeprecations[`${version}/${message}`]) return;\n\n  console.log(`Deprecated as of ${version}. ${message}`);\n  seenDeprecations[`${version}/${message}`] = true;\n};\n\n/*\nSyntax highlighting with language autodetection.\nhttps://highlightjs.org/\n*/\n\nconst escape$1 = escapeHTML;\nconst inherit$1 = inherit;\nconst NO_MATCH = Symbol(\"nomatch\");\n\n/**\n * @param {any} hljs - object that is extended (legacy)\n * @returns {HLJSApi}\n */\nconst HLJS = function(hljs) {\n  // Global internal variables used within the highlight.js library.\n  /** @type {Record<string, Language>} */\n  const languages = Object.create(null);\n  /** @type {Record<string, string>} */\n  const aliases = Object.create(null);\n  /** @type {HLJSPlugin[]} */\n  const plugins = [];\n\n  // safe/production mode - swallows more errors, tries to keep running\n  // even if a single syntax or parse hits a fatal error\n  let SAFE_MODE = true;\n  const fixMarkupRe = /(^(<[^>]+>|\\t|)+|\\n)/gm;\n  const LANGUAGE_NOT_FOUND = \"Could not find the language '{}', did you forget to load/include a language module?\";\n  /** @type {Language} */\n  const PLAINTEXT_LANGUAGE = { disableAutodetect: true, name: 'Plain text', contains: [] };\n\n  // Global options used when within external APIs. This is modified when\n  // calling the `hljs.configure` function.\n  /** @type HLJSOptions */\n  let options = {\n    noHighlightRe: /^(no-?highlight)$/i,\n    languageDetectRe: /\\blang(?:uage)?-([\\w-]+)\\b/i,\n    classPrefix: 'hljs-',\n    tabReplace: null,\n    useBR: false,\n    languages: null,\n    // beta configuration options, subject to change, welcome to discuss\n    // https://github.com/highlightjs/highlight.js/issues/1086\n    __emitter: TokenTreeEmitter\n  };\n\n  /* Utility functions */\n\n  /**\n   * Tests a language name to see if highlighting should be skipped\n   * @param {string} languageName\n   */\n  function shouldNotHighlight(languageName) {\n    return options.noHighlightRe.test(languageName);\n  }\n\n  /**\n   * @param {HighlightedHTMLElement} block - the HTML element to determine language for\n   */\n  function blockLanguage(block) {\n    let classes = block.className + ' ';\n\n    classes += block.parentNode ? block.parentNode.className : '';\n\n    // language-* takes precedence over non-prefixed class names.\n    const match = options.languageDetectRe.exec(classes);\n    if (match) {\n      const language = getLanguage(match[1]);\n      if (!language) {\n        warn(LANGUAGE_NOT_FOUND.replace(\"{}\", match[1]));\n        warn(\"Falling back to no-highlight mode for this block.\", block);\n      }\n      return language ? match[1] : 'no-highlight';\n    }\n\n    return classes\n      .split(/\\s+/)\n      .find((_class) => shouldNotHighlight(_class) || getLanguage(_class));\n  }\n\n  /**\n   * Core highlighting function.\n   *\n   * OLD API\n   * highlight(lang, code, ignoreIllegals, continuation)\n   *\n   * NEW API\n   * highlight(code, {lang, ignoreIllegals})\n   *\n   * @param {string} codeOrlanguageName - the language to use for highlighting\n   * @param {string | HighlightOptions} optionsOrCode - the code to highlight\n   * @param {boolean} [ignoreIllegals] - whether to ignore illegal matches, default is to bail\n   * @param {CompiledMode} [continuation] - current continuation mode, if any\n   *\n   * @returns {HighlightResult} Result - an object that represents the result\n   * @property {string} language - the language name\n   * @property {number} relevance - the relevance score\n   * @property {string} value - the highlighted HTML code\n   * @property {string} code - the original raw code\n   * @property {CompiledMode} top - top of the current mode stack\n   * @property {boolean} illegal - indicates whether any illegal matches were found\n  */\n  function highlight(codeOrlanguageName, optionsOrCode, ignoreIllegals, continuation) {\n    let code = \"\";\n    let languageName = \"\";\n    if (typeof optionsOrCode === \"object\") {\n      code = codeOrlanguageName;\n      ignoreIllegals = optionsOrCode.ignoreIllegals;\n      languageName = optionsOrCode.language;\n      // continuation not supported at all via the new API\n      // eslint-disable-next-line no-undefined\n      continuation = undefined;\n    } else {\n      // old API\n      deprecated(\"10.7.0\", \"highlight(lang, code, ...args) has been deprecated.\");\n      deprecated(\"10.7.0\", \"Please use highlight(code, options) instead.\\nhttps://github.com/highlightjs/highlight.js/issues/2277\");\n      languageName = codeOrlanguageName;\n      code = optionsOrCode;\n    }\n\n    /** @type {BeforeHighlightContext} */\n    const context = {\n      code,\n      language: languageName\n    };\n    // the plugin can change the desired language or the code to be highlighted\n    // just be changing the object it was passed\n    fire(\"before:highlight\", context);\n\n    // a before plugin can usurp the result completely by providing it's own\n    // in which case we don't even need to call highlight\n    const result = context.result\n      ? context.result\n      : _highlight(context.language, context.code, ignoreIllegals, continuation);\n\n    result.code = context.code;\n    // the plugin can change anything in result to suite it\n    fire(\"after:highlight\", result);\n\n    return result;\n  }\n\n  /**\n   * private highlight that's used internally and does not fire callbacks\n   *\n   * @param {string} languageName - the language to use for highlighting\n   * @param {string} codeToHighlight - the code to highlight\n   * @param {boolean?} [ignoreIllegals] - whether to ignore illegal matches, default is to bail\n   * @param {CompiledMode?} [continuation] - current continuation mode, if any\n   * @returns {HighlightResult} - result of the highlight operation\n  */\n  function _highlight(languageName, codeToHighlight, ignoreIllegals, continuation) {\n    /**\n     * Return keyword data if a match is a keyword\n     * @param {CompiledMode} mode - current mode\n     * @param {RegExpMatchArray} match - regexp match data\n     * @returns {KeywordData | false}\n     */\n    function keywordData(mode, match) {\n      const matchText = language.case_insensitive ? match[0].toLowerCase() : match[0];\n      return Object.prototype.hasOwnProperty.call(mode.keywords, matchText) && mode.keywords[matchText];\n    }\n\n    function processKeywords() {\n      if (!top.keywords) {\n        emitter.addText(modeBuffer);\n        return;\n      }\n\n      let lastIndex = 0;\n      top.keywordPatternRe.lastIndex = 0;\n      let match = top.keywordPatternRe.exec(modeBuffer);\n      let buf = \"\";\n\n      while (match) {\n        buf += modeBuffer.substring(lastIndex, match.index);\n        const data = keywordData(top, match);\n        if (data) {\n          const [kind, keywordRelevance] = data;\n          emitter.addText(buf);\n          buf = \"\";\n\n          relevance += keywordRelevance;\n          if (kind.startsWith(\"_\")) {\n            // _ implied for relevance only, do not highlight\n            // by applying a class name\n            buf += match[0];\n          } else {\n            const cssClass = language.classNameAliases[kind] || kind;\n            emitter.addKeyword(match[0], cssClass);\n          }\n        } else {\n          buf += match[0];\n        }\n        lastIndex = top.keywordPatternRe.lastIndex;\n        match = top.keywordPatternRe.exec(modeBuffer);\n      }\n      buf += modeBuffer.substr(lastIndex);\n      emitter.addText(buf);\n    }\n\n    function processSubLanguage() {\n      if (modeBuffer === \"\") return;\n      /** @type HighlightResult */\n      let result = null;\n\n      if (typeof top.subLanguage === 'string') {\n        if (!languages[top.subLanguage]) {\n          emitter.addText(modeBuffer);\n          return;\n        }\n        result = _highlight(top.subLanguage, modeBuffer, true, continuations[top.subLanguage]);\n        continuations[top.subLanguage] = /** @type {CompiledMode} */ (result.top);\n      } else {\n        result = highlightAuto(modeBuffer, top.subLanguage.length ? top.subLanguage : null);\n      }\n\n      // Counting embedded language score towards the host language may be disabled\n      // with zeroing the containing mode relevance. Use case in point is Markdown that\n      // allows XML everywhere and makes every XML snippet to have a much larger Markdown\n      // score.\n      if (top.relevance > 0) {\n        relevance += result.relevance;\n      }\n      emitter.addSublanguage(result.emitter, result.language);\n    }\n\n    function processBuffer() {\n      if (top.subLanguage != null) {\n        processSubLanguage();\n      } else {\n        processKeywords();\n      }\n      modeBuffer = '';\n    }\n\n    /**\n     * @param {Mode} mode - new mode to start\n     */\n    function startNewMode(mode) {\n      if (mode.className) {\n        emitter.openNode(language.classNameAliases[mode.className] || mode.className);\n      }\n      top = Object.create(mode, { parent: { value: top } });\n      return top;\n    }\n\n    /**\n     * @param {CompiledMode } mode - the mode to potentially end\n     * @param {RegExpMatchArray} match - the latest match\n     * @param {string} matchPlusRemainder - match plus remainder of content\n     * @returns {CompiledMode | void} - the next mode, or if void continue on in current mode\n     */\n    function endOfMode(mode, match, matchPlusRemainder) {\n      let matched = startsWith(mode.endRe, matchPlusRemainder);\n\n      if (matched) {\n        if (mode[\"on:end\"]) {\n          const resp = new Response(mode);\n          mode[\"on:end\"](match, resp);\n          if (resp.isMatchIgnored) matched = false;\n        }\n\n        if (matched) {\n          while (mode.endsParent && mode.parent) {\n            mode = mode.parent;\n          }\n          return mode;\n        }\n      }\n      // even if on:end fires an `ignore` it's still possible\n      // that we might trigger the end node because of a parent mode\n      if (mode.endsWithParent) {\n        return endOfMode(mode.parent, match, matchPlusRemainder);\n      }\n    }\n\n    /**\n     * Handle matching but then ignoring a sequence of text\n     *\n     * @param {string} lexeme - string containing full match text\n     */\n    function doIgnore(lexeme) {\n      if (top.matcher.regexIndex === 0) {\n        // no more regexs to potentially match here, so we move the cursor forward one\n        // space\n        modeBuffer += lexeme[0];\n        return 1;\n      } else {\n        // no need to move the cursor, we still have additional regexes to try and\n        // match at this very spot\n        resumeScanAtSamePosition = true;\n        return 0;\n      }\n    }\n\n    /**\n     * Handle the start of a new potential mode match\n     *\n     * @param {EnhancedMatch} match - the current match\n     * @returns {number} how far to advance the parse cursor\n     */\n    function doBeginMatch(match) {\n      const lexeme = match[0];\n      const newMode = match.rule;\n\n      const resp = new Response(newMode);\n      // first internal before callbacks, then the public ones\n      const beforeCallbacks = [newMode.__beforeBegin, newMode[\"on:begin\"]];\n      for (const cb of beforeCallbacks) {\n        if (!cb) continue;\n        cb(match, resp);\n        if (resp.isMatchIgnored) return doIgnore(lexeme);\n      }\n\n      if (newMode && newMode.endSameAsBegin) {\n        newMode.endRe = escape(lexeme);\n      }\n\n      if (newMode.skip) {\n        modeBuffer += lexeme;\n      } else {\n        if (newMode.excludeBegin) {\n          modeBuffer += lexeme;\n        }\n        processBuffer();\n        if (!newMode.returnBegin && !newMode.excludeBegin) {\n          modeBuffer = lexeme;\n        }\n      }\n      startNewMode(newMode);\n      // if (mode[\"after:begin\"]) {\n      //   let resp = new Response(mode);\n      //   mode[\"after:begin\"](match, resp);\n      // }\n      return newMode.returnBegin ? 0 : lexeme.length;\n    }\n\n    /**\n     * Handle the potential end of mode\n     *\n     * @param {RegExpMatchArray} match - the current match\n     */\n    function doEndMatch(match) {\n      const lexeme = match[0];\n      const matchPlusRemainder = codeToHighlight.substr(match.index);\n\n      const endMode = endOfMode(top, match, matchPlusRemainder);\n      if (!endMode) { return NO_MATCH; }\n\n      const origin = top;\n      if (origin.skip) {\n        modeBuffer += lexeme;\n      } else {\n        if (!(origin.returnEnd || origin.excludeEnd)) {\n          modeBuffer += lexeme;\n        }\n        processBuffer();\n        if (origin.excludeEnd) {\n          modeBuffer = lexeme;\n        }\n      }\n      do {\n        if (top.className) {\n          emitter.closeNode();\n        }\n        if (!top.skip && !top.subLanguage) {\n          relevance += top.relevance;\n        }\n        top = top.parent;\n      } while (top !== endMode.parent);\n      if (endMode.starts) {\n        if (endMode.endSameAsBegin) {\n          endMode.starts.endRe = endMode.endRe;\n        }\n        startNewMode(endMode.starts);\n      }\n      return origin.returnEnd ? 0 : lexeme.length;\n    }\n\n    function processContinuations() {\n      const list = [];\n      for (let current = top; current !== language; current = current.parent) {\n        if (current.className) {\n          list.unshift(current.className);\n        }\n      }\n      list.forEach(item => emitter.openNode(item));\n    }\n\n    /** @type {{type?: MatchType, index?: number, rule?: Mode}}} */\n    let lastMatch = {};\n\n    /**\n     *  Process an individual match\n     *\n     * @param {string} textBeforeMatch - text preceeding the match (since the last match)\n     * @param {EnhancedMatch} [match] - the match itself\n     */\n    function processLexeme(textBeforeMatch, match) {\n      const lexeme = match && match[0];\n\n      // add non-matched text to the current mode buffer\n      modeBuffer += textBeforeMatch;\n\n      if (lexeme == null) {\n        processBuffer();\n        return 0;\n      }\n\n      // we've found a 0 width match and we're stuck, so we need to advance\n      // this happens when we have badly behaved rules that have optional matchers to the degree that\n      // sometimes they can end up matching nothing at all\n      // Ref: https://github.com/highlightjs/highlight.js/issues/2140\n      if (lastMatch.type === \"begin\" && match.type === \"end\" && lastMatch.index === match.index && lexeme === \"\") {\n        // spit the \"skipped\" character that our regex choked on back into the output sequence\n        modeBuffer += codeToHighlight.slice(match.index, match.index + 1);\n        if (!SAFE_MODE) {\n          /** @type {AnnotatedError} */\n          const err = new Error('0 width match regex');\n          err.languageName = languageName;\n          err.badRule = lastMatch.rule;\n          throw err;\n        }\n        return 1;\n      }\n      lastMatch = match;\n\n      if (match.type === \"begin\") {\n        return doBeginMatch(match);\n      } else if (match.type === \"illegal\" && !ignoreIllegals) {\n        // illegal match, we do not continue processing\n        /** @type {AnnotatedError} */\n        const err = new Error('Illegal lexeme \"' + lexeme + '\" for mode \"' + (top.className || '<unnamed>') + '\"');\n        err.mode = top;\n        throw err;\n      } else if (match.type === \"end\") {\n        const processed = doEndMatch(match);\n        if (processed !== NO_MATCH) {\n          return processed;\n        }\n      }\n\n      // edge case for when illegal matches $ (end of line) which is technically\n      // a 0 width match but not a begin/end match so it's not caught by the\n      // first handler (when ignoreIllegals is true)\n      if (match.type === \"illegal\" && lexeme === \"\") {\n        // advance so we aren't stuck in an infinite loop\n        return 1;\n      }\n\n      // infinite loops are BAD, this is a last ditch catch all. if we have a\n      // decent number of iterations yet our index (cursor position in our\n      // parsing) still 3x behind our index then something is very wrong\n      // so we bail\n      if (iterations > 100000 && iterations > match.index * 3) {\n        const err = new Error('potential infinite loop, way more iterations than matches');\n        throw err;\n      }\n\n      /*\n      Why might be find ourselves here?  Only one occasion now.  An end match that was\n      triggered but could not be completed.  When might this happen?  When an `endSameasBegin`\n      rule sets the end rule to a specific match.  Since the overall mode termination rule that's\n      being used to scan the text isn't recompiled that means that any match that LOOKS like\n      the end (but is not, because it is not an exact match to the beginning) will\n      end up here.  A definite end match, but when `doEndMatch` tries to \"reapply\"\n      the end rule and fails to match, we wind up here, and just silently ignore the end.\n\n      This causes no real harm other than stopping a few times too many.\n      */\n\n      modeBuffer += lexeme;\n      return lexeme.length;\n    }\n\n    const language = getLanguage(languageName);\n    if (!language) {\n      error(LANGUAGE_NOT_FOUND.replace(\"{}\", languageName));\n      throw new Error('Unknown language: \"' + languageName + '\"');\n    }\n\n    const md = compileLanguage(language, { plugins });\n    let result = '';\n    /** @type {CompiledMode} */\n    let top = continuation || md;\n    /** @type Record<string,CompiledMode> */\n    const continuations = {}; // keep continuations for sub-languages\n    const emitter = new options.__emitter(options);\n    processContinuations();\n    let modeBuffer = '';\n    let relevance = 0;\n    let index = 0;\n    let iterations = 0;\n    let resumeScanAtSamePosition = false;\n\n    try {\n      top.matcher.considerAll();\n\n      for (;;) {\n        iterations++;\n        if (resumeScanAtSamePosition) {\n          // only regexes not matched previously will now be\n          // considered for a potential match\n          resumeScanAtSamePosition = false;\n        } else {\n          top.matcher.considerAll();\n        }\n        top.matcher.lastIndex = index;\n\n        const match = top.matcher.exec(codeToHighlight);\n        // console.log(\"match\", match[0], match.rule && match.rule.begin)\n\n        if (!match) break;\n\n        const beforeMatch = codeToHighlight.substring(index, match.index);\n        const processedCount = processLexeme(beforeMatch, match);\n        index = match.index + processedCount;\n      }\n      processLexeme(codeToHighlight.substr(index));\n      emitter.closeAllNodes();\n      emitter.finalize();\n      result = emitter.toHTML();\n\n      return {\n        // avoid possible breakage with v10 clients expecting\n        // this to always be an integer\n        relevance: Math.floor(relevance),\n        value: result,\n        language: languageName,\n        illegal: false,\n        emitter: emitter,\n        top: top\n      };\n    } catch (err) {\n      if (err.message && err.message.includes('Illegal')) {\n        return {\n          illegal: true,\n          illegalBy: {\n            msg: err.message,\n            context: codeToHighlight.slice(index - 100, index + 100),\n            mode: err.mode\n          },\n          sofar: result,\n          relevance: 0,\n          value: escape$1(codeToHighlight),\n          emitter: emitter\n        };\n      } else if (SAFE_MODE) {\n        return {\n          illegal: false,\n          relevance: 0,\n          value: escape$1(codeToHighlight),\n          emitter: emitter,\n          language: languageName,\n          top: top,\n          errorRaised: err\n        };\n      } else {\n        throw err;\n      }\n    }\n  }\n\n  /**\n   * returns a valid highlight result, without actually doing any actual work,\n   * auto highlight starts with this and it's possible for small snippets that\n   * auto-detection may not find a better match\n   * @param {string} code\n   * @returns {HighlightResult}\n   */\n  function justTextHighlightResult(code) {\n    const result = {\n      relevance: 0,\n      emitter: new options.__emitter(options),\n      value: escape$1(code),\n      illegal: false,\n      top: PLAINTEXT_LANGUAGE\n    };\n    result.emitter.addText(code);\n    return result;\n  }\n\n  /**\n  Highlighting with language detection. Accepts a string with the code to\n  highlight. Returns an object with the following properties:\n\n  - language (detected language)\n  - relevance (int)\n  - value (an HTML string with highlighting markup)\n  - second_best (object with the same structure for second-best heuristically\n    detected language, may be absent)\n\n    @param {string} code\n    @param {Array<string>} [languageSubset]\n    @returns {AutoHighlightResult}\n  */\n  function highlightAuto(code, languageSubset) {\n    languageSubset = languageSubset || options.languages || Object.keys(languages);\n    const plaintext = justTextHighlightResult(code);\n\n    const results = languageSubset.filter(getLanguage).filter(autoDetection).map(name =>\n      _highlight(name, code, false)\n    );\n    results.unshift(plaintext); // plaintext is always an option\n\n    const sorted = results.sort((a, b) => {\n      // sort base on relevance\n      if (a.relevance !== b.relevance) return b.relevance - a.relevance;\n\n      // always award the tie to the base language\n      // ie if C++ and Arduino are tied, it's more likely to be C++\n      if (a.language && b.language) {\n        if (getLanguage(a.language).supersetOf === b.language) {\n          return 1;\n        } else if (getLanguage(b.language).supersetOf === a.language) {\n          return -1;\n        }\n      }\n\n      // otherwise say they are equal, which has the effect of sorting on\n      // relevance while preserving the original ordering - which is how ties\n      // have historically been settled, ie the language that comes first always\n      // wins in the case of a tie\n      return 0;\n    });\n\n    const [best, secondBest] = sorted;\n\n    /** @type {AutoHighlightResult} */\n    const result = best;\n    result.second_best = secondBest;\n\n    return result;\n  }\n\n  /**\n  Post-processing of the highlighted markup:\n\n  - replace TABs with something more useful\n  - replace real line-breaks with '<br>' for non-pre containers\n\n    @param {string} html\n    @returns {string}\n  */\n  function fixMarkup(html) {\n    if (!(options.tabReplace || options.useBR)) {\n      return html;\n    }\n\n    return html.replace(fixMarkupRe, match => {\n      if (match === '\\n') {\n        return options.useBR ? '<br>' : match;\n      } else if (options.tabReplace) {\n        return match.replace(/\\t/g, options.tabReplace);\n      }\n      return match;\n    });\n  }\n\n  /**\n   * Builds new class name for block given the language name\n   *\n   * @param {HTMLElement} element\n   * @param {string} [currentLang]\n   * @param {string} [resultLang]\n   */\n  function updateClassName(element, currentLang, resultLang) {\n    const language = currentLang ? aliases[currentLang] : resultLang;\n\n    element.classList.add(\"hljs\");\n    if (language) element.classList.add(language);\n  }\n\n  /** @type {HLJSPlugin} */\n  const brPlugin = {\n    \"before:highlightElement\": ({ el }) => {\n      if (options.useBR) {\n        el.innerHTML = el.innerHTML.replace(/\\n/g, '').replace(/<br[ /]*>/g, '\\n');\n      }\n    },\n    \"after:highlightElement\": ({ result }) => {\n      if (options.useBR) {\n        result.value = result.value.replace(/\\n/g, \"<br>\");\n      }\n    }\n  };\n\n  const TAB_REPLACE_RE = /^(<[^>]+>|\\t)+/gm;\n  /** @type {HLJSPlugin} */\n  const tabReplacePlugin = {\n    \"after:highlightElement\": ({ result }) => {\n      if (options.tabReplace) {\n        result.value = result.value.replace(TAB_REPLACE_RE, (m) =>\n          m.replace(/\\t/g, options.tabReplace)\n        );\n      }\n    }\n  };\n\n  /**\n   * Applies highlighting to a DOM node containing code. Accepts a DOM node and\n   * two optional parameters for fixMarkup.\n   *\n   * @param {HighlightedHTMLElement} element - the HTML element to highlight\n  */\n  function highlightElement(element) {\n    /** @type HTMLElement */\n    let node = null;\n    const language = blockLanguage(element);\n\n    if (shouldNotHighlight(language)) return;\n\n    // support for v10 API\n    fire(\"before:highlightElement\",\n      { el: element, language: language });\n\n    node = element;\n    const text = node.textContent;\n    const result = language ? highlight(text, { language, ignoreIllegals: true }) : highlightAuto(text);\n\n    // support for v10 API\n    fire(\"after:highlightElement\", { el: element, result, text });\n\n    element.innerHTML = result.value;\n    updateClassName(element, language, result.language);\n    element.result = {\n      language: result.language,\n      // TODO: remove with version 11.0\n      re: result.relevance,\n      relavance: result.relevance\n    };\n    if (result.second_best) {\n      element.second_best = {\n        language: result.second_best.language,\n        // TODO: remove with version 11.0\n        re: result.second_best.relevance,\n        relavance: result.second_best.relevance\n      };\n    }\n  }\n\n  /**\n   * Updates highlight.js global options with the passed options\n   *\n   * @param {Partial<HLJSOptions>} userOptions\n   */\n  function configure(userOptions) {\n    if (userOptions.useBR) {\n      deprecated(\"10.3.0\", \"'useBR' will be removed entirely in v11.0\");\n      deprecated(\"10.3.0\", \"Please see https://github.com/highlightjs/highlight.js/issues/2559\");\n    }\n    options = inherit$1(options, userOptions);\n  }\n\n  /**\n   * Highlights to all <pre><code> blocks on a page\n   *\n   * @type {Function & {called?: boolean}}\n   */\n  // TODO: remove v12, deprecated\n  const initHighlighting = () => {\n    if (initHighlighting.called) return;\n    initHighlighting.called = true;\n\n    deprecated(\"10.6.0\", \"initHighlighting() is deprecated.  Use highlightAll() instead.\");\n\n    const blocks = document.querySelectorAll('pre code');\n    blocks.forEach(highlightElement);\n  };\n\n  // Higlights all when DOMContentLoaded fires\n  // TODO: remove v12, deprecated\n  function initHighlightingOnLoad() {\n    deprecated(\"10.6.0\", \"initHighlightingOnLoad() is deprecated.  Use highlightAll() instead.\");\n    wantsHighlight = true;\n  }\n\n  let wantsHighlight = false;\n\n  /**\n   * auto-highlights all pre>code elements on the page\n   */\n  function highlightAll() {\n    // if we are called too early in the loading process\n    if (document.readyState === \"loading\") {\n      wantsHighlight = true;\n      return;\n    }\n\n    const blocks = document.querySelectorAll('pre code');\n    blocks.forEach(highlightElement);\n  }\n\n  function boot() {\n    // if a highlight was requested before DOM was loaded, do now\n    if (wantsHighlight) highlightAll();\n  }\n\n  // make sure we are in the browser environment\n  if (typeof window !== 'undefined' && window.addEventListener) {\n    window.addEventListener('DOMContentLoaded', boot, false);\n  }\n\n  /**\n   * Register a language grammar module\n   *\n   * @param {string} languageName\n   * @param {LanguageFn} languageDefinition\n   */\n  function registerLanguage(languageName, languageDefinition) {\n    let lang = null;\n    try {\n      lang = languageDefinition(hljs);\n    } catch (error$1) {\n      error(\"Language definition for '{}' could not be registered.\".replace(\"{}\", languageName));\n      // hard or soft error\n      if (!SAFE_MODE) { throw error$1; } else { error(error$1); }\n      // languages that have serious errors are replaced with essentially a\n      // \"plaintext\" stand-in so that the code blocks will still get normal\n      // css classes applied to them - and one bad language won't break the\n      // entire highlighter\n      lang = PLAINTEXT_LANGUAGE;\n    }\n    // give it a temporary name if it doesn't have one in the meta-data\n    if (!lang.name) lang.name = languageName;\n    languages[languageName] = lang;\n    lang.rawDefinition = languageDefinition.bind(null, hljs);\n\n    if (lang.aliases) {\n      registerAliases(lang.aliases, { languageName });\n    }\n  }\n\n  /**\n   * Remove a language grammar module\n   *\n   * @param {string} languageName\n   */\n  function unregisterLanguage(languageName) {\n    delete languages[languageName];\n    for (const alias of Object.keys(aliases)) {\n      if (aliases[alias] === languageName) {\n        delete aliases[alias];\n      }\n    }\n  }\n\n  /**\n   * @returns {string[]} List of language internal names\n   */\n  function listLanguages() {\n    return Object.keys(languages);\n  }\n\n  /**\n    intended usage: When one language truly requires another\n\n    Unlike `getLanguage`, this will throw when the requested language\n    is not available.\n\n    @param {string} name - name of the language to fetch/require\n    @returns {Language | never}\n  */\n  function requireLanguage(name) {\n    deprecated(\"10.4.0\", \"requireLanguage will be removed entirely in v11.\");\n    deprecated(\"10.4.0\", \"Please see https://github.com/highlightjs/highlight.js/pull/2844\");\n\n    const lang = getLanguage(name);\n    if (lang) { return lang; }\n\n    const err = new Error('The \\'{}\\' language is required, but not loaded.'.replace('{}', name));\n    throw err;\n  }\n\n  /**\n   * @param {string} name - name of the language to retrieve\n   * @returns {Language | undefined}\n   */\n  function getLanguage(name) {\n    name = (name || '').toLowerCase();\n    return languages[name] || languages[aliases[name]];\n  }\n\n  /**\n   *\n   * @param {string|string[]} aliasList - single alias or list of aliases\n   * @param {{languageName: string}} opts\n   */\n  function registerAliases(aliasList, { languageName }) {\n    if (typeof aliasList === 'string') {\n      aliasList = [aliasList];\n    }\n    aliasList.forEach(alias => { aliases[alias.toLowerCase()] = languageName; });\n  }\n\n  /**\n   * Determines if a given language has auto-detection enabled\n   * @param {string} name - name of the language\n   */\n  function autoDetection(name) {\n    const lang = getLanguage(name);\n    return lang && !lang.disableAutodetect;\n  }\n\n  /**\n   * Upgrades the old highlightBlock plugins to the new\n   * highlightElement API\n   * @param {HLJSPlugin} plugin\n   */\n  function upgradePluginAPI(plugin) {\n    // TODO: remove with v12\n    if (plugin[\"before:highlightBlock\"] && !plugin[\"before:highlightElement\"]) {\n      plugin[\"before:highlightElement\"] = (data) => {\n        plugin[\"before:highlightBlock\"](\n          Object.assign({ block: data.el }, data)\n        );\n      };\n    }\n    if (plugin[\"after:highlightBlock\"] && !plugin[\"after:highlightElement\"]) {\n      plugin[\"after:highlightElement\"] = (data) => {\n        plugin[\"after:highlightBlock\"](\n          Object.assign({ block: data.el }, data)\n        );\n      };\n    }\n  }\n\n  /**\n   * @param {HLJSPlugin} plugin\n   */\n  function addPlugin(plugin) {\n    upgradePluginAPI(plugin);\n    plugins.push(plugin);\n  }\n\n  /**\n   *\n   * @param {PluginEvent} event\n   * @param {any} args\n   */\n  function fire(event, args) {\n    const cb = event;\n    plugins.forEach(function(plugin) {\n      if (plugin[cb]) {\n        plugin[cb](args);\n      }\n    });\n  }\n\n  /**\n  Note: fixMarkup is deprecated and will be removed entirely in v11\n\n  @param {string} arg\n  @returns {string}\n  */\n  function deprecateFixMarkup(arg) {\n    deprecated(\"10.2.0\", \"fixMarkup will be removed entirely in v11.0\");\n    deprecated(\"10.2.0\", \"Please see https://github.com/highlightjs/highlight.js/issues/2534\");\n\n    return fixMarkup(arg);\n  }\n\n  /**\n   *\n   * @param {HighlightedHTMLElement} el\n   */\n  function deprecateHighlightBlock(el) {\n    deprecated(\"10.7.0\", \"highlightBlock will be removed entirely in v12.0\");\n    deprecated(\"10.7.0\", \"Please use highlightElement now.\");\n\n    return highlightElement(el);\n  }\n\n  /* Interface definition */\n  Object.assign(hljs, {\n    highlight,\n    highlightAuto,\n    highlightAll,\n    fixMarkup: deprecateFixMarkup,\n    highlightElement,\n    // TODO: Remove with v12 API\n    highlightBlock: deprecateHighlightBlock,\n    configure,\n    initHighlighting,\n    initHighlightingOnLoad,\n    registerLanguage,\n    unregisterLanguage,\n    listLanguages,\n    getLanguage,\n    registerAliases,\n    requireLanguage,\n    autoDetection,\n    inherit: inherit$1,\n    addPlugin,\n    // plugins for frameworks\n    vuePlugin: BuildVuePlugin(hljs).VuePlugin\n  });\n\n  hljs.debugMode = function() { SAFE_MODE = false; };\n  hljs.safeMode = function() { SAFE_MODE = true; };\n  hljs.versionString = version;\n\n  for (const key in MODES) {\n    // @ts-ignore\n    if (typeof MODES[key] === \"object\") {\n      // @ts-ignore\n      deepFreezeEs6(MODES[key]);\n    }\n  }\n\n  // merge all the modes/regexs into our main object\n  Object.assign(hljs, MODES);\n\n  // built-in plugins, likely to be moved out of core in the future\n  hljs.addPlugin(brPlugin); // slated to be removed in v11\n  hljs.addPlugin(mergeHTMLPlugin);\n  hljs.addPlugin(tabReplacePlugin);\n  return hljs;\n};\n\n// export an \"instance\" of the highlighter\nvar highlight = HLJS({});\n\nmodule.exports = highlight;\n", "//\n// format - printf-like string formatting for JavaScript\n// github.com/samsonjs/format\n// @_sjs\n//\n// Copyright 2010 - 2013 <PERSON> <<EMAIL>>\n//\n// MIT License\n// http://sjs.mit-license.org\n//\n\n;(function() {\n\n  //// Export the API\n  var namespace;\n\n  // CommonJS / Node module\n  if (typeof module !== 'undefined') {\n    namespace = module.exports = format;\n  }\n\n  // Browsers and other environments\n  else {\n    // Get the global object. Works in ES3, ES5, and ES5 strict mode.\n    namespace = (function(){ return this || (1,eval)('this') }());\n  }\n\n  namespace.format = format;\n  namespace.vsprintf = vsprintf;\n\n  if (typeof console !== 'undefined' && typeof console.log === 'function') {\n    namespace.printf = printf;\n  }\n\n  function printf(/* ... */) {\n    console.log(format.apply(null, arguments));\n  }\n\n  function vsprintf(fmt, replacements) {\n    return format.apply(null, [fmt].concat(replacements));\n  }\n\n  function format(fmt) {\n    var argIndex = 1 // skip initial format argument\n      , args = [].slice.call(arguments)\n      , i = 0\n      , n = fmt.length\n      , result = ''\n      , c\n      , escaped = false\n      , arg\n      , tmp\n      , leadingZero = false\n      , precision\n      , nextArg = function() { return args[argIndex++]; }\n      , slurpNumber = function() {\n          var digits = '';\n          while (/\\d/.test(fmt[i])) {\n            digits += fmt[i++];\n            c = fmt[i];\n          }\n          return digits.length > 0 ? parseInt(digits) : null;\n        }\n      ;\n    for (; i < n; ++i) {\n      c = fmt[i];\n      if (escaped) {\n        escaped = false;\n        if (c == '.') {\n          leadingZero = false;\n          c = fmt[++i];\n        }\n        else if (c == '0' && fmt[i + 1] == '.') {\n          leadingZero = true;\n          i += 2;\n          c = fmt[i];\n        }\n        else {\n          leadingZero = true;\n        }\n        precision = slurpNumber();\n        switch (c) {\n        case 'b': // number in binary\n          result += parseInt(nextArg(), 10).toString(2);\n          break;\n        case 'c': // character\n          arg = nextArg();\n          if (typeof arg === 'string' || arg instanceof String)\n            result += arg;\n          else\n            result += String.fromCharCode(parseInt(arg, 10));\n          break;\n        case 'd': // number in decimal\n          result += parseInt(nextArg(), 10);\n          break;\n        case 'f': // floating point number\n          tmp = String(parseFloat(nextArg()).toFixed(precision || 6));\n          result += leadingZero ? tmp : tmp.replace(/^0/, '');\n          break;\n        case 'j': // JSON\n          result += JSON.stringify(nextArg());\n          break;\n        case 'o': // number in octal\n          result += '0' + parseInt(nextArg(), 10).toString(8);\n          break;\n        case 's': // string\n          result += nextArg();\n          break;\n        case 'x': // lowercase hexadecimal\n          result += '0x' + parseInt(nextArg(), 10).toString(16);\n          break;\n        case 'X': // uppercase hexadecimal\n          result += '0x' + parseInt(nextArg(), 10).toString(16).toUpperCase();\n          break;\n        default:\n          result += c;\n          break;\n        }\n      } else if (c === '%') {\n        escaped = true;\n      } else {\n        result += c;\n      }\n    }\n    return result;\n  }\n\n}());\n", "'use strict'\n\nvar formatter = require('format')\n\nvar fault = create(Error)\n\nmodule.exports = fault\n\nfault.eval = create(EvalError)\nfault.range = create(RangeError)\nfault.reference = create(ReferenceError)\nfault.syntax = create(SyntaxError)\nfault.type = create(TypeError)\nfault.uri = create(URIError)\n\nfault.create = create\n\n// Create a new `EConstructor`, with the formatted `format` as a first argument.\nfunction create(EConstructor) {\n  FormattedError.displayName = EConstructor.displayName || EConstructor.name\n\n  return FormattedError\n\n  function FormattedError(format) {\n    if (format) {\n      format = formatter.apply(null, arguments)\n    }\n\n    return new EConstructor(format)\n  }\n}\n", "'use strict'\n\nvar high = require('highlight.js/lib/core')\nvar fault = require('fault')\n\nexports.highlight = highlight\nexports.highlightAuto = highlightAuto\nexports.registerLanguage = registerLanguage\nexports.listLanguages = listLanguages\nexports.registerAlias = registerAlias\n\nEmitter.prototype.addText = text\nEmitter.prototype.addKeyword = addKeyword\nEmitter.prototype.addSublanguage = addSublanguage\nEmitter.prototype.openNode = open\nEmitter.prototype.closeNode = close\nEmitter.prototype.closeAllNodes = noop\nEmitter.prototype.finalize = noop\nEmitter.prototype.toHTML = toHtmlNoop\n\nvar defaultPrefix = 'hljs-'\n\n// Highlighting `value` in the language `name`.\nfunction highlight(name, value, options) {\n  var before = high.configure({})\n  var settings = options || {}\n  var prefix = settings.prefix\n  var result\n\n  if (typeof name !== 'string') {\n    throw fault('Expected `string` for name, got `%s`', name)\n  }\n\n  if (!high.getLanguage(name)) {\n    throw fault('Unknown language: `%s` is not registered', name)\n  }\n\n  if (typeof value !== 'string') {\n    throw fault('Expected `string` for value, got `%s`', value)\n  }\n\n  if (prefix === null || prefix === undefined) {\n    prefix = defaultPrefix\n  }\n\n  high.configure({__emitter: Emitter, classPrefix: prefix})\n\n  result = high.highlight(value, {language: name, ignoreIllegals: true})\n\n  high.configure(before || {})\n\n  /* istanbul ignore if - Highlight.js seems to use this (currently) for broken\n   * grammars, so let’s keep it in there just to be sure. */\n  if (result.errorRaised) {\n    throw result.errorRaised\n  }\n\n  return {\n    relevance: result.relevance,\n    language: result.language,\n    value: result.emitter.rootNode.children\n  }\n}\n\nfunction highlightAuto(value, options) {\n  var settings = options || {}\n  var subset = settings.subset || high.listLanguages()\n  var prefix = settings.prefix\n  var length = subset.length\n  var index = -1\n  var result\n  var secondBest\n  var current\n  var name\n\n  if (prefix === null || prefix === undefined) {\n    prefix = defaultPrefix\n  }\n\n  if (typeof value !== 'string') {\n    throw fault('Expected `string` for value, got `%s`', value)\n  }\n\n  secondBest = {relevance: 0, language: null, value: []}\n  result = {relevance: 0, language: null, value: []}\n\n  while (++index < length) {\n    name = subset[index]\n\n    if (!high.getLanguage(name)) {\n      continue\n    }\n\n    current = highlight(name, value, options)\n    current.language = name\n\n    if (current.relevance > secondBest.relevance) {\n      secondBest = current\n    }\n\n    if (current.relevance > result.relevance) {\n      secondBest = result\n      result = current\n    }\n  }\n\n  if (secondBest.language) {\n    result.secondBest = secondBest\n  }\n\n  return result\n}\n\n// Register a language.\nfunction registerLanguage(name, syntax) {\n  high.registerLanguage(name, syntax)\n}\n\n// Get a list of all registered languages.\nfunction listLanguages() {\n  return high.listLanguages()\n}\n\n// Register more aliases for an already registered language.\nfunction registerAlias(name, alias) {\n  var map = name\n  var key\n\n  if (alias) {\n    map = {}\n    map[name] = alias\n  }\n\n  for (key in map) {\n    high.registerAliases(map[key], {languageName: key})\n  }\n}\n\nfunction Emitter(options) {\n  this.options = options\n  this.rootNode = {children: []}\n  this.stack = [this.rootNode]\n}\n\nfunction addKeyword(value, name) {\n  this.openNode(name)\n  this.addText(value)\n  this.closeNode()\n}\n\nfunction addSublanguage(other, name) {\n  var stack = this.stack\n  var current = stack[stack.length - 1]\n  var results = other.rootNode.children\n  var node = name\n    ? {\n        type: 'element',\n        tagName: 'span',\n        properties: {className: [name]},\n        children: results\n      }\n    : results\n\n  current.children = current.children.concat(node)\n}\n\nfunction text(value) {\n  var stack = this.stack\n  var current\n  var tail\n\n  if (value === '') return\n\n  current = stack[stack.length - 1]\n  tail = current.children[current.children.length - 1]\n\n  if (tail && tail.type === 'text') {\n    tail.value += value\n  } else {\n    current.children.push({type: 'text', value: value})\n  }\n}\n\nfunction open(name) {\n  var stack = this.stack\n  var className = this.options.classPrefix + name\n  var current = stack[stack.length - 1]\n  var child = {\n    type: 'element',\n    tagName: 'span',\n    properties: {className: [className]},\n    children: []\n  }\n\n  current.children.push(child)\n  stack.push(child)\n}\n\nfunction close() {\n  this.stack.pop()\n}\n\nfunction toHtmlNoop() {\n  return ''\n}\n\nfunction noop() {}\n"], "mappings": ";;;;;AAAA;AAAA;AAAA,aAAS,WAAW,KAAK;AACrB,UAAI,eAAe,KAAK;AACpB,YAAI,QAAQ,IAAI,SAAS,IAAI,MAAM,WAAY;AAC3C,gBAAM,IAAI,MAAM,kBAAkB;AAAA,QACtC;AAAA,MACJ,WAAW,eAAe,KAAK;AAC3B,YAAI,MAAM,IAAI,QAAQ,IAAI,SAAS,WAAY;AAC3C,gBAAM,IAAI,MAAM,kBAAkB;AAAA,QACtC;AAAA,MACJ;AAGA,aAAO,OAAO,GAAG;AAEjB,aAAO,oBAAoB,GAAG,EAAE,QAAQ,SAAU,MAAM;AACpD,YAAI,OAAO,IAAI,IAAI;AAGnB,YAAI,OAAO,QAAQ,YAAY,CAAC,OAAO,SAAS,IAAI,GAAG;AACnD,qBAAW,IAAI;AAAA,QACnB;AAAA,MACJ,CAAC;AAED,aAAO;AAAA,IACX;AAEA,QAAI,gBAAgB;AACpB,QAAI,WAAW;AACf,kBAAc,UAAU;AAGxB,QAAM,WAAN,MAAe;AAAA;AAAA;AAAA;AAAA,MAIb,YAAY,MAAM;AAEhB,YAAI,KAAK,SAAS,OAAW,MAAK,OAAO,CAAC;AAE1C,aAAK,OAAO,KAAK;AACjB,aAAK,iBAAiB;AAAA,MACxB;AAAA,MAEA,cAAc;AACZ,aAAK,iBAAiB;AAAA,MACxB;AAAA,IACF;AAMA,aAAS,WAAW,OAAO;AACzB,aAAO,MACJ,QAAQ,MAAM,OAAO,EACrB,QAAQ,MAAM,MAAM,EACpB,QAAQ,MAAM,MAAM,EACpB,QAAQ,MAAM,QAAQ,EACtB,QAAQ,MAAM,QAAQ;AAAA,IAC3B;AAUA,aAAS,QAAQ,aAAa,SAAS;AAErC,YAAM,SAAS,uBAAO,OAAO,IAAI;AAEjC,iBAAW,OAAO,UAAU;AAC1B,eAAO,GAAG,IAAI,SAAS,GAAG;AAAA,MAC5B;AACA,cAAQ,QAAQ,SAAS,KAAK;AAC5B,mBAAW,OAAO,KAAK;AACrB,iBAAO,GAAG,IAAI,IAAI,GAAG;AAAA,QACvB;AAAA,MACF,CAAC;AACD;AAAA;AAAA,QAAyB;AAAA;AAAA,IAC3B;AAcA,QAAM,aAAa;AAMnB,QAAM,oBAAoB,CAAC,SAAS;AAClC,aAAO,CAAC,CAAC,KAAK;AAAA,IAChB;AAGA,QAAM,eAAN,MAAmB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAOjB,YAAY,WAAW,SAAS;AAC9B,aAAK,SAAS;AACd,aAAK,cAAc,QAAQ;AAC3B,kBAAU,KAAK,IAAI;AAAA,MACrB;AAAA;AAAA;AAAA;AAAA;AAAA,MAMA,QAAQ,MAAM;AACZ,aAAK,UAAU,WAAW,IAAI;AAAA,MAChC;AAAA;AAAA;AAAA;AAAA;AAAA,MAMA,SAAS,MAAM;AACb,YAAI,CAAC,kBAAkB,IAAI,EAAG;AAE9B,YAAI,YAAY,KAAK;AACrB,YAAI,CAAC,KAAK,aAAa;AACrB,sBAAY,GAAG,KAAK,WAAW,GAAG,SAAS;AAAA,QAC7C;AACA,aAAK,KAAK,SAAS;AAAA,MACrB;AAAA;AAAA;AAAA;AAAA;AAAA,MAMA,UAAU,MAAM;AACd,YAAI,CAAC,kBAAkB,IAAI,EAAG;AAE9B,aAAK,UAAU;AAAA,MACjB;AAAA;AAAA;AAAA;AAAA,MAKA,QAAQ;AACN,eAAO,KAAK;AAAA,MACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAQA,KAAK,WAAW;AACd,aAAK,UAAU,gBAAgB,SAAS;AAAA,MAC1C;AAAA,IACF;AAMA,QAAM,YAAN,MAAM,WAAU;AAAA,MACd,cAAc;AAEZ,aAAK,WAAW,EAAE,UAAU,CAAC,EAAE;AAC/B,aAAK,QAAQ,CAAC,KAAK,QAAQ;AAAA,MAC7B;AAAA,MAEA,IAAI,MAAM;AACR,eAAO,KAAK,MAAM,KAAK,MAAM,SAAS,CAAC;AAAA,MACzC;AAAA,MAEA,IAAI,OAAO;AAAE,eAAO,KAAK;AAAA,MAAU;AAAA;AAAA,MAGnC,IAAI,MAAM;AACR,aAAK,IAAI,SAAS,KAAK,IAAI;AAAA,MAC7B;AAAA;AAAA,MAGA,SAAS,MAAM;AAEb,cAAM,OAAO,EAAE,MAAM,UAAU,CAAC,EAAE;AAClC,aAAK,IAAI,IAAI;AACb,aAAK,MAAM,KAAK,IAAI;AAAA,MACtB;AAAA,MAEA,YAAY;AACV,YAAI,KAAK,MAAM,SAAS,GAAG;AACzB,iBAAO,KAAK,MAAM,IAAI;AAAA,QACxB;AAEA,eAAO;AAAA,MACT;AAAA,MAEA,gBAAgB;AACd,eAAO,KAAK,UAAU,EAAE;AAAA,MAC1B;AAAA,MAEA,SAAS;AACP,eAAO,KAAK,UAAU,KAAK,UAAU,MAAM,CAAC;AAAA,MAC9C;AAAA;AAAA;AAAA;AAAA;AAAA,MAMA,KAAK,SAAS;AAEZ,eAAO,KAAK,YAAY,MAAM,SAAS,KAAK,QAAQ;AAAA,MAGtD;AAAA;AAAA;AAAA;AAAA;AAAA,MAMA,OAAO,MAAM,SAAS,MAAM;AAC1B,YAAI,OAAO,SAAS,UAAU;AAC5B,kBAAQ,QAAQ,IAAI;AAAA,QACtB,WAAW,KAAK,UAAU;AACxB,kBAAQ,SAAS,IAAI;AACrB,eAAK,SAAS,QAAQ,CAAC,UAAU,KAAK,MAAM,SAAS,KAAK,CAAC;AAC3D,kBAAQ,UAAU,IAAI;AAAA,QACxB;AACA,eAAO;AAAA,MACT;AAAA;AAAA;AAAA;AAAA,MAKA,OAAO,UAAU,MAAM;AACrB,YAAI,OAAO,SAAS,SAAU;AAC9B,YAAI,CAAC,KAAK,SAAU;AAEpB,YAAI,KAAK,SAAS,MAAM,QAAM,OAAO,OAAO,QAAQ,GAAG;AAGrD,eAAK,WAAW,CAAC,KAAK,SAAS,KAAK,EAAE,CAAC;AAAA,QACzC,OAAO;AACL,eAAK,SAAS,QAAQ,CAAC,UAAU;AAC/B,uBAAU,UAAU,KAAK;AAAA,UAC3B,CAAC;AAAA,QACH;AAAA,MACF;AAAA,IACF;AAsBA,QAAM,mBAAN,cAA+B,UAAU;AAAA;AAAA;AAAA;AAAA,MAIvC,YAAY,SAAS;AACnB,cAAM;AACN,aAAK,UAAU;AAAA,MACjB;AAAA;AAAA;AAAA;AAAA;AAAA,MAMA,WAAW,MAAM,MAAM;AACrB,YAAI,SAAS,IAAI;AAAE;AAAA,QAAQ;AAE3B,aAAK,SAAS,IAAI;AAClB,aAAK,QAAQ,IAAI;AACjB,aAAK,UAAU;AAAA,MACjB;AAAA;AAAA;AAAA;AAAA,MAKA,QAAQ,MAAM;AACZ,YAAI,SAAS,IAAI;AAAE;AAAA,QAAQ;AAE3B,aAAK,IAAI,IAAI;AAAA,MACf;AAAA;AAAA;AAAA;AAAA;AAAA,MAMA,eAAe,SAAS,MAAM;AAE5B,cAAM,OAAO,QAAQ;AACrB,aAAK,OAAO;AACZ,aAAK,cAAc;AACnB,aAAK,IAAI,IAAI;AAAA,MACf;AAAA,MAEA,SAAS;AACP,cAAM,WAAW,IAAI,aAAa,MAAM,KAAK,OAAO;AACpD,eAAO,SAAS,MAAM;AAAA,MACxB;AAAA,MAEA,WAAW;AACT,eAAO;AAAA,MACT;AAAA,IACF;AAMA,aAAS,OAAO,OAAO;AACrB,aAAO,IAAI,OAAO,MAAM,QAAQ,yBAAyB,MAAM,GAAG,GAAG;AAAA,IACvE;AAMA,aAAS,OAAO,IAAI;AAClB,UAAI,CAAC,GAAI,QAAO;AAChB,UAAI,OAAO,OAAO,SAAU,QAAO;AAEnC,aAAO,GAAG;AAAA,IACZ;AAMA,aAAS,UAAU,MAAM;AACvB,YAAM,SAAS,KAAK,IAAI,CAAC,MAAM,OAAO,CAAC,CAAC,EAAE,KAAK,EAAE;AACjD,aAAO;AAAA,IACT;AASA,aAAS,UAAU,MAAM;AACvB,YAAM,SAAS,MAAM,KAAK,IAAI,CAAC,MAAM,OAAO,CAAC,CAAC,EAAE,KAAK,GAAG,IAAI;AAC5D,aAAO;AAAA,IACT;AAMA,aAAS,iBAAiB,IAAI;AAC5B,aAAQ,IAAI,OAAO,GAAG,SAAS,IAAI,GAAG,EAAG,KAAK,EAAE,EAAE,SAAS;AAAA,IAC7D;AAOA,aAAS,WAAW,IAAI,QAAQ;AAC9B,YAAM,QAAQ,MAAM,GAAG,KAAK,MAAM;AAClC,aAAO,SAAS,MAAM,UAAU;AAAA,IAClC;AASA,QAAM,aAAa;AAYnB,aAAS,KAAK,SAAS,YAAY,KAAK;AACtC,UAAI,cAAc;AAElB,aAAO,QAAQ,IAAI,CAAC,UAAU;AAC5B,uBAAe;AACf,cAAM,SAAS;AACf,YAAI,KAAK,OAAO,KAAK;AACrB,YAAI,MAAM;AAEV,eAAO,GAAG,SAAS,GAAG;AACpB,gBAAM,QAAQ,WAAW,KAAK,EAAE;AAChC,cAAI,CAAC,OAAO;AACV,mBAAO;AACP;AAAA,UACF;AACA,iBAAO,GAAG,UAAU,GAAG,MAAM,KAAK;AAClC,eAAK,GAAG,UAAU,MAAM,QAAQ,MAAM,CAAC,EAAE,MAAM;AAC/C,cAAI,MAAM,CAAC,EAAE,CAAC,MAAM,QAAQ,MAAM,CAAC,GAAG;AAEpC,mBAAO,OAAO,OAAO,OAAO,MAAM,CAAC,CAAC,IAAI,MAAM;AAAA,UAChD,OAAO;AACL,mBAAO,MAAM,CAAC;AACd,gBAAI,MAAM,CAAC,MAAM,KAAK;AACpB;AAAA,YACF;AAAA,UACF;AAAA,QACF;AACA,eAAO;AAAA,MACT,CAAC,EAAE,IAAI,QAAM,IAAI,EAAE,GAAG,EAAE,KAAK,SAAS;AAAA,IACxC;AAGA,QAAM,mBAAmB;AACzB,QAAM,WAAW;AACjB,QAAM,sBAAsB;AAC5B,QAAM,YAAY;AAClB,QAAM,cAAc;AACpB,QAAM,mBAAmB;AACzB,QAAM,iBAAiB;AAKvB,QAAM,UAAU,CAAC,OAAO,CAAC,MAAM;AAC7B,YAAM,eAAe;AACrB,UAAI,KAAK,QAAQ;AACf,aAAK,QAAQ;AAAA,UACX;AAAA,UACA;AAAA,UACA,KAAK;AAAA,UACL;AAAA,QAAM;AAAA,MACV;AACA,aAAO,QAAQ;AAAA,QACb,WAAW;AAAA,QACX,OAAO;AAAA,QACP,KAAK;AAAA,QACL,WAAW;AAAA;AAAA,QAEX,YAAY,CAAC,GAAG,SAAS;AACvB,cAAI,EAAE,UAAU,EAAG,MAAK,YAAY;AAAA,QACtC;AAAA,MACF,GAAG,IAAI;AAAA,IACT;AAGA,QAAM,mBAAmB;AAAA,MACvB,OAAO;AAAA,MAAgB,WAAW;AAAA,IACpC;AACA,QAAM,mBAAmB;AAAA,MACvB,WAAW;AAAA,MACX,OAAO;AAAA,MACP,KAAK;AAAA,MACL,SAAS;AAAA,MACT,UAAU,CAAC,gBAAgB;AAAA,IAC7B;AACA,QAAM,oBAAoB;AAAA,MACxB,WAAW;AAAA,MACX,OAAO;AAAA,MACP,KAAK;AAAA,MACL,SAAS;AAAA,MACT,UAAU,CAAC,gBAAgB;AAAA,IAC7B;AACA,QAAM,qBAAqB;AAAA,MACzB,OAAO;AAAA,IACT;AASA,QAAM,UAAU,SAAS,OAAO,KAAK,cAAc,CAAC,GAAG;AACrD,YAAM,OAAO;AAAA,QACX;AAAA,UACE,WAAW;AAAA,UACX;AAAA,UACA;AAAA,UACA,UAAU,CAAC;AAAA,QACb;AAAA,QACA;AAAA,MACF;AACA,WAAK,SAAS,KAAK,kBAAkB;AACrC,WAAK,SAAS,KAAK;AAAA,QACjB,WAAW;AAAA,QACX,OAAO;AAAA,QACP,WAAW;AAAA,MACb,CAAC;AACD,aAAO;AAAA,IACT;AACA,QAAM,sBAAsB,QAAQ,MAAM,GAAG;AAC7C,QAAM,uBAAuB,QAAQ,QAAQ,MAAM;AACnD,QAAM,oBAAoB,QAAQ,KAAK,GAAG;AAC1C,QAAM,cAAc;AAAA,MAClB,WAAW;AAAA,MACX,OAAO;AAAA,MACP,WAAW;AAAA,IACb;AACA,QAAM,gBAAgB;AAAA,MACpB,WAAW;AAAA,MACX,OAAO;AAAA,MACP,WAAW;AAAA,IACb;AACA,QAAM,qBAAqB;AAAA,MACzB,WAAW;AAAA,MACX,OAAO;AAAA,MACP,WAAW;AAAA,IACb;AACA,QAAM,kBAAkB;AAAA,MACtB,WAAW;AAAA,MACX,OAAO,YAAY;AAAA,MASnB,WAAW;AAAA,IACb;AACA,QAAM,cAAc;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAOlB,OAAO;AAAA,MACP,UAAU,CAAC;AAAA,QACT,WAAW;AAAA,QACX,OAAO;AAAA,QACP,KAAK;AAAA,QACL,SAAS;AAAA,QACT,UAAU;AAAA,UACR;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,YACL,WAAW;AAAA,YACX,UAAU,CAAC,gBAAgB;AAAA,UAC7B;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH;AACA,QAAM,aAAa;AAAA,MACjB,WAAW;AAAA,MACX,OAAO;AAAA,MACP,WAAW;AAAA,IACb;AACA,QAAM,wBAAwB;AAAA,MAC5B,WAAW;AAAA,MACX,OAAO;AAAA,MACP,WAAW;AAAA,IACb;AACA,QAAM,eAAe;AAAA;AAAA,MAEnB,OAAO,YAAY;AAAA,MACnB,WAAW;AAAA,IACb;AASA,QAAM,oBAAoB,SAAS,MAAM;AACvC,aAAO,OAAO;AAAA,QAAO;AAAA,QACnB;AAAA;AAAA,UAEE,YAAY,CAAC,GAAG,SAAS;AAAE,iBAAK,KAAK,cAAc,EAAE,CAAC;AAAA,UAAG;AAAA;AAAA,UAEzD,UAAU,CAAC,GAAG,SAAS;AAAE,gBAAI,KAAK,KAAK,gBAAgB,EAAE,CAAC,EAAG,MAAK,YAAY;AAAA,UAAG;AAAA,QACnF;AAAA,MAAC;AAAA,IACL;AAEA,QAAI,QAAqB,OAAO,OAAO;AAAA,MACnC,WAAW;AAAA,MACX;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ,CAAC;AA0BD,aAAS,sBAAsB,OAAO,UAAU;AAC9C,YAAM,SAAS,MAAM,MAAM,MAAM,QAAQ,CAAC;AAC1C,UAAI,WAAW,KAAK;AAClB,iBAAS,YAAY;AAAA,MACvB;AAAA,IACF;AAOA,aAAS,cAAc,MAAM,QAAQ;AACnC,UAAI,CAAC,OAAQ;AACb,UAAI,CAAC,KAAK,cAAe;AAOzB,WAAK,QAAQ,SAAS,KAAK,cAAc,MAAM,GAAG,EAAE,KAAK,GAAG,IAAI;AAChE,WAAK,gBAAgB;AACrB,WAAK,WAAW,KAAK,YAAY,KAAK;AACtC,aAAO,KAAK;AAKZ,UAAI,KAAK,cAAc,OAAW,MAAK,YAAY;AAAA,IACrD;AAMA,aAAS,eAAe,MAAM,SAAS;AACrC,UAAI,CAAC,MAAM,QAAQ,KAAK,OAAO,EAAG;AAElC,WAAK,UAAU,OAAO,GAAG,KAAK,OAAO;AAAA,IACvC;AAMA,aAAS,aAAa,MAAM,SAAS;AACnC,UAAI,CAAC,KAAK,MAAO;AACjB,UAAI,KAAK,SAAS,KAAK,IAAK,OAAM,IAAI,MAAM,0CAA0C;AAEtF,WAAK,QAAQ,KAAK;AAClB,aAAO,KAAK;AAAA,IACd;AAMA,aAAS,iBAAiB,MAAM,SAAS;AAEvC,UAAI,KAAK,cAAc,OAAW,MAAK,YAAY;AAAA,IACrD;AAGA,QAAM,kBAAkB;AAAA,MACtB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA;AAAA,MACA;AAAA;AAAA,MACA;AAAA;AAAA,IACF;AAEA,QAAM,4BAA4B;AAQlC,aAAS,gBAAgB,aAAa,iBAAiB,YAAY,2BAA2B;AAE5F,YAAM,mBAAmB,CAAC;AAI1B,UAAI,OAAO,gBAAgB,UAAU;AACnC,oBAAY,WAAW,YAAY,MAAM,GAAG,CAAC;AAAA,MAC/C,WAAW,MAAM,QAAQ,WAAW,GAAG;AACrC,oBAAY,WAAW,WAAW;AAAA,MACpC,OAAO;AACL,eAAO,KAAK,WAAW,EAAE,QAAQ,SAASA,YAAW;AAEnD,iBAAO;AAAA,YACL;AAAA,YACA,gBAAgB,YAAYA,UAAS,GAAG,iBAAiBA,UAAS;AAAA,UACpE;AAAA,QACF,CAAC;AAAA,MACH;AACA,aAAO;AAYP,eAAS,YAAYA,YAAW,aAAa;AAC3C,YAAI,iBAAiB;AACnB,wBAAc,YAAY,IAAI,OAAK,EAAE,YAAY,CAAC;AAAA,QACpD;AACA,oBAAY,QAAQ,SAAS,SAAS;AACpC,gBAAM,OAAO,QAAQ,MAAM,GAAG;AAC9B,2BAAiB,KAAK,CAAC,CAAC,IAAI,CAACA,YAAW,gBAAgB,KAAK,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC;AAAA,QAC3E,CAAC;AAAA,MACH;AAAA,IACF;AAUA,aAAS,gBAAgB,SAAS,eAAe;AAG/C,UAAI,eAAe;AACjB,eAAO,OAAO,aAAa;AAAA,MAC7B;AAEA,aAAO,cAAc,OAAO,IAAI,IAAI;AAAA,IACtC;AAMA,aAAS,cAAc,SAAS;AAC9B,aAAO,gBAAgB,SAAS,QAAQ,YAAY,CAAC;AAAA,IACvD;AAaA,aAAS,gBAAgB,UAAU,EAAE,QAAQ,GAAG;AAO9C,eAAS,OAAO,OAAO,QAAQ;AAC7B,eAAO,IAAI;AAAA,UACT,OAAO,KAAK;AAAA,UACZ,OAAO,SAAS,mBAAmB,MAAM,OAAO,SAAS,MAAM;AAAA,QACjE;AAAA,MACF;AAAA,MAeA,MAAM,WAAW;AAAA,QACf,cAAc;AACZ,eAAK,eAAe,CAAC;AAErB,eAAK,UAAU,CAAC;AAChB,eAAK,UAAU;AACf,eAAK,WAAW;AAAA,QAClB;AAAA;AAAA,QAGA,QAAQ,IAAI,MAAM;AAChB,eAAK,WAAW,KAAK;AAErB,eAAK,aAAa,KAAK,OAAO,IAAI;AAClC,eAAK,QAAQ,KAAK,CAAC,MAAM,EAAE,CAAC;AAC5B,eAAK,WAAW,iBAAiB,EAAE,IAAI;AAAA,QACzC;AAAA,QAEA,UAAU;AACR,cAAI,KAAK,QAAQ,WAAW,GAAG;AAG7B,iBAAK,OAAO,MAAM;AAAA,UACpB;AACA,gBAAM,cAAc,KAAK,QAAQ,IAAI,QAAM,GAAG,CAAC,CAAC;AAChD,eAAK,YAAY,OAAO,KAAK,WAAW,GAAG,IAAI;AAC/C,eAAK,YAAY;AAAA,QACnB;AAAA;AAAA,QAGA,KAAK,GAAG;AACN,eAAK,UAAU,YAAY,KAAK;AAChC,gBAAM,QAAQ,KAAK,UAAU,KAAK,CAAC;AACnC,cAAI,CAAC,OAAO;AAAE,mBAAO;AAAA,UAAM;AAG3B,gBAAM,IAAI,MAAM,UAAU,CAAC,IAAIC,OAAMA,KAAI,KAAK,OAAO,MAAS;AAE9D,gBAAM,YAAY,KAAK,aAAa,CAAC;AAGrC,gBAAM,OAAO,GAAG,CAAC;AAEjB,iBAAO,OAAO,OAAO,OAAO,SAAS;AAAA,QACvC;AAAA,MACF;AAAA,MAiCA,MAAM,oBAAoB;AAAA,QACxB,cAAc;AAEZ,eAAK,QAAQ,CAAC;AAEd,eAAK,eAAe,CAAC;AACrB,eAAK,QAAQ;AAEb,eAAK,YAAY;AACjB,eAAK,aAAa;AAAA,QACpB;AAAA;AAAA,QAGA,WAAW,OAAO;AAChB,cAAI,KAAK,aAAa,KAAK,EAAG,QAAO,KAAK,aAAa,KAAK;AAE5D,gBAAM,UAAU,IAAI,WAAW;AAC/B,eAAK,MAAM,MAAM,KAAK,EAAE,QAAQ,CAAC,CAAC,IAAI,IAAI,MAAM,QAAQ,QAAQ,IAAI,IAAI,CAAC;AACzE,kBAAQ,QAAQ;AAChB,eAAK,aAAa,KAAK,IAAI;AAC3B,iBAAO;AAAA,QACT;AAAA,QAEA,6BAA6B;AAC3B,iBAAO,KAAK,eAAe;AAAA,QAC7B;AAAA,QAEA,cAAc;AACZ,eAAK,aAAa;AAAA,QACpB;AAAA;AAAA,QAGA,QAAQ,IAAI,MAAM;AAChB,eAAK,MAAM,KAAK,CAAC,IAAI,IAAI,CAAC;AAC1B,cAAI,KAAK,SAAS,QAAS,MAAK;AAAA,QAClC;AAAA;AAAA,QAGA,KAAK,GAAG;AACN,gBAAM,IAAI,KAAK,WAAW,KAAK,UAAU;AACzC,YAAE,YAAY,KAAK;AACnB,cAAI,SAAS,EAAE,KAAK,CAAC;AAiCrB,cAAI,KAAK,2BAA2B,GAAG;AACrC,gBAAI,UAAU,OAAO,UAAU,KAAK,UAAW;AAAA,iBAAO;AACpD,oBAAM,KAAK,KAAK,WAAW,CAAC;AAC5B,iBAAG,YAAY,KAAK,YAAY;AAChC,uBAAS,GAAG,KAAK,CAAC;AAAA,YACpB;AAAA,UACF;AAEA,cAAI,QAAQ;AACV,iBAAK,cAAc,OAAO,WAAW;AACrC,gBAAI,KAAK,eAAe,KAAK,OAAO;AAElC,mBAAK,YAAY;AAAA,YACnB;AAAA,UACF;AAEA,iBAAO;AAAA,QACT;AAAA,MACF;AASA,eAAS,eAAe,MAAM;AAC5B,cAAM,KAAK,IAAI,oBAAoB;AAEnC,aAAK,SAAS,QAAQ,UAAQ,GAAG,QAAQ,KAAK,OAAO,EAAE,MAAM,MAAM,MAAM,QAAQ,CAAC,CAAC;AAEnF,YAAI,KAAK,eAAe;AACtB,aAAG,QAAQ,KAAK,eAAe,EAAE,MAAM,MAAM,CAAC;AAAA,QAChD;AACA,YAAI,KAAK,SAAS;AAChB,aAAG,QAAQ,KAAK,SAAS,EAAE,MAAM,UAAU,CAAC;AAAA,QAC9C;AAEA,eAAO;AAAA,MACT;AAyCA,eAAS,YAAY,MAAM,QAAQ;AACjC,cAAM;AAAA;AAAA,UAAmC;AAAA;AACzC,YAAI,KAAK,WAAY,QAAO;AAE5B;AAAA;AAAA;AAAA,UAGE;AAAA,QACF,EAAE,QAAQ,SAAO,IAAI,MAAM,MAAM,CAAC;AAElC,iBAAS,mBAAmB,QAAQ,SAAO,IAAI,MAAM,MAAM,CAAC;AAG5D,aAAK,gBAAgB;AAErB;AAAA,UACE;AAAA;AAAA;AAAA,UAGA;AAAA;AAAA,UAEA;AAAA,QACF,EAAE,QAAQ,SAAO,IAAI,MAAM,MAAM,CAAC;AAElC,aAAK,aAAa;AAElB,YAAI,iBAAiB;AACrB,YAAI,OAAO,KAAK,aAAa,UAAU;AACrC,2BAAiB,KAAK,SAAS;AAC/B,iBAAO,KAAK,SAAS;AAAA,QACvB;AAEA,YAAI,KAAK,UAAU;AACjB,eAAK,WAAW,gBAAgB,KAAK,UAAU,SAAS,gBAAgB;AAAA,QAC1E;AAGA,YAAI,KAAK,WAAW,gBAAgB;AAClC,gBAAM,IAAI,MAAM,gGAAgG;AAAA,QAClH;AAIA,yBAAiB,kBAAkB,KAAK,WAAW;AACnD,cAAM,mBAAmB,OAAO,gBAAgB,IAAI;AAEpD,YAAI,QAAQ;AACV,cAAI,CAAC,KAAK,MAAO,MAAK,QAAQ;AAC9B,gBAAM,UAAU,OAAO,KAAK,KAAK;AACjC,cAAI,KAAK,eAAgB,MAAK,MAAM,KAAK;AACzC,cAAI,CAAC,KAAK,OAAO,CAAC,KAAK,eAAgB,MAAK,MAAM;AAClD,cAAI,KAAK,IAAK,OAAM,QAAQ,OAAO,KAAK,GAAG;AAC3C,gBAAM,gBAAgB,OAAO,KAAK,GAAG,KAAK;AAC1C,cAAI,KAAK,kBAAkB,OAAO,eAAe;AAC/C,kBAAM,kBAAkB,KAAK,MAAM,MAAM,MAAM,OAAO;AAAA,UACxD;AAAA,QACF;AACA,YAAI,KAAK,QAAS,OAAM,YAAY;AAAA;AAAA,UAAuC,KAAK;AAAA,QAAQ;AACxF,YAAI,CAAC,KAAK,SAAU,MAAK,WAAW,CAAC;AAErC,aAAK,WAAW,CAAC,EAAE,OAAO,GAAG,KAAK,SAAS,IAAI,SAAS,GAAG;AACzD,iBAAO,kBAAkB,MAAM,SAAS,OAAO,CAAC;AAAA,QAClD,CAAC,CAAC;AACF,aAAK,SAAS,QAAQ,SAAS,GAAG;AAAE;AAAA;AAAA,YAA+B;AAAA,YAAI;AAAA,UAAK;AAAA,QAAG,CAAC;AAEhF,YAAI,KAAK,QAAQ;AACf,sBAAY,KAAK,QAAQ,MAAM;AAAA,QACjC;AAEA,cAAM,UAAU,eAAe,KAAK;AACpC,eAAO;AAAA,MACT;AAEA,UAAI,CAAC,SAAS,mBAAoB,UAAS,qBAAqB,CAAC;AAGjE,UAAI,SAAS,YAAY,SAAS,SAAS,SAAS,MAAM,GAAG;AAC3D,cAAM,IAAI,MAAM,2FAA2F;AAAA,MAC7G;AAGA,eAAS,mBAAmB,QAAQ,SAAS,oBAAoB,CAAC,CAAC;AAEnE,aAAO;AAAA;AAAA,QAA+B;AAAA,MAAS;AAAA,IACjD;AAaA,aAAS,mBAAmB,MAAM;AAChC,UAAI,CAAC,KAAM,QAAO;AAElB,aAAO,KAAK,kBAAkB,mBAAmB,KAAK,MAAM;AAAA,IAC9D;AAYA,aAAS,kBAAkB,MAAM;AAC/B,UAAI,KAAK,YAAY,CAAC,KAAK,gBAAgB;AACzC,aAAK,iBAAiB,KAAK,SAAS,IAAI,SAAS,SAAS;AACxD,iBAAO,QAAQ,MAAM,EAAE,UAAU,KAAK,GAAG,OAAO;AAAA,QAClD,CAAC;AAAA,MACH;AAKA,UAAI,KAAK,gBAAgB;AACvB,eAAO,KAAK;AAAA,MACd;AAMA,UAAI,mBAAmB,IAAI,GAAG;AAC5B,eAAO,QAAQ,MAAM,EAAE,QAAQ,KAAK,SAAS,QAAQ,KAAK,MAAM,IAAI,KAAK,CAAC;AAAA,MAC5E;AAEA,UAAI,OAAO,SAAS,IAAI,GAAG;AACzB,eAAO,QAAQ,IAAI;AAAA,MACrB;AAGA,aAAO;AAAA,IACT;AAEA,QAAI,UAAU;AAId,aAAS,yBAAyB,OAAO;AACvC,aAAO,QAAQ,SAAS,UAAU,EAAE;AAAA,IACtC;AAEA,aAAS,eAAe,MAAM;AAC5B,YAAM,YAAY;AAAA,QAChB,OAAO,CAAC,YAAY,QAAQ,YAAY;AAAA,QACxC,MAAM,WAAW;AACf,iBAAO;AAAA,YACL,kBAAkB;AAAA,YAClB,iBAAiB;AAAA,UACnB;AAAA,QACF;AAAA,QACA,UAAU;AAAA,UACR,YAAY;AACV,gBAAI,KAAK,gBAAiB,QAAO;AAEjC,mBAAO,UAAU,KAAK;AAAA,UACxB;AAAA,UACA,cAAc;AAEZ,gBAAI,CAAC,KAAK,cAAc,CAAC,KAAK,YAAY,KAAK,QAAQ,GAAG;AACxD,sBAAQ,KAAK,iBAAiB,KAAK,QAAQ,qCAAqC;AAChF,mBAAK,kBAAkB;AACvB,qBAAO,WAAW,KAAK,IAAI;AAAA,YAC7B;AAEA,gBAAI,SAAS,CAAC;AACd,gBAAI,KAAK,YAAY;AACnB,uBAAS,KAAK,cAAc,KAAK,IAAI;AACrC,mBAAK,mBAAmB,OAAO;AAAA,YACjC,OAAO;AACL,uBAAS,KAAK,UAAU,KAAK,UAAU,KAAK,MAAM,KAAK,cAAc;AACrE,mBAAK,mBAAmB,KAAK;AAAA,YAC/B;AACA,mBAAO,OAAO;AAAA,UAChB;AAAA,UACA,aAAa;AACX,mBAAO,CAAC,KAAK,YAAY,yBAAyB,KAAK,UAAU;AAAA,UACnE;AAAA,UACA,iBAAiB;AACf,mBAAO;AAAA,UACT;AAAA,QACF;AAAA;AAAA;AAAA,QAGA,OAAO,eAAe;AACpB,iBAAO,cAAc,OAAO,CAAC,GAAG;AAAA,YAC9B,cAAc,QAAQ;AAAA,cACpB,OAAO,KAAK;AAAA,cACZ,UAAU,EAAE,WAAW,KAAK,YAAY;AAAA,YAC1C,CAAC;AAAA,UACH,CAAC;AAAA,QACH;AAAA;AAAA,MAEF;AAEA,YAAM,YAAY;AAAA,QAChB,QAAQ,KAAK;AACX,cAAI,UAAU,eAAe,SAAS;AAAA,QACxC;AAAA,MACF;AAEA,aAAO,EAAE,WAAW,UAAU;AAAA,IAChC;AAKA,QAAM,kBAAkB;AAAA,MACtB,0BAA0B,CAAC,EAAE,IAAI,QAAQ,KAAK,MAAM;AAClD,cAAM,iBAAiB,WAAW,EAAE;AACpC,YAAI,CAAC,eAAe,OAAQ;AAE5B,cAAM,aAAa,SAAS,cAAc,KAAK;AAC/C,mBAAW,YAAY,OAAO;AAC9B,eAAO,QAAQ,aAAa,gBAAgB,WAAW,UAAU,GAAG,IAAI;AAAA,MAC1E;AAAA,IACF;AAcA,aAAS,IAAI,MAAM;AACjB,aAAO,KAAK,SAAS,YAAY;AAAA,IACnC;AAKA,aAAS,WAAW,MAAM;AAExB,YAAM,SAAS,CAAC;AAChB,OAAC,SAAS,YAAYC,OAAM,QAAQ;AAClC,iBAAS,QAAQA,MAAK,YAAY,OAAO,QAAQ,MAAM,aAAa;AAClE,cAAI,MAAM,aAAa,GAAG;AACxB,sBAAU,MAAM,UAAU;AAAA,UAC5B,WAAW,MAAM,aAAa,GAAG;AAC/B,mBAAO,KAAK;AAAA,cACV,OAAO;AAAA,cACP;AAAA,cACA,MAAM;AAAA,YACR,CAAC;AACD,qBAAS,YAAY,OAAO,MAAM;AAIlC,gBAAI,CAAC,IAAI,KAAK,EAAE,MAAM,iBAAiB,GAAG;AACxC,qBAAO,KAAK;AAAA,gBACV,OAAO;AAAA,gBACP;AAAA,gBACA,MAAM;AAAA,cACR,CAAC;AAAA,YACH;AAAA,UACF;AAAA,QACF;AACA,eAAO;AAAA,MACT,GAAG,MAAM,CAAC;AACV,aAAO;AAAA,IACT;AAOA,aAAS,aAAa,UAAU,aAAa,OAAO;AAClD,UAAI,YAAY;AAChB,UAAI,SAAS;AACb,YAAM,YAAY,CAAC;AAEnB,eAAS,eAAe;AACtB,YAAI,CAAC,SAAS,UAAU,CAAC,YAAY,QAAQ;AAC3C,iBAAO,SAAS,SAAS,WAAW;AAAA,QACtC;AACA,YAAI,SAAS,CAAC,EAAE,WAAW,YAAY,CAAC,EAAE,QAAQ;AAChD,iBAAQ,SAAS,CAAC,EAAE,SAAS,YAAY,CAAC,EAAE,SAAU,WAAW;AAAA,QACnE;AAiBA,eAAO,YAAY,CAAC,EAAE,UAAU,UAAU,WAAW;AAAA,MACvD;AAKA,eAAS,KAAK,MAAM;AAElB,iBAAS,gBAAgB,MAAM;AAC7B,iBAAO,MAAM,KAAK,WAAW,OAAO,WAAW,KAAK,KAAK,IAAI;AAAA,QAC/D;AAEA,kBAAU,MAAM,IAAI,IAAI,IAAI,CAAC,EAAE,IAAI,KAAK,KAAK,YAAY,eAAe,EAAE,KAAK,EAAE,IAAI;AAAA,MACvF;AAKA,eAAS,MAAM,MAAM;AACnB,kBAAU,OAAO,IAAI,IAAI,IAAI;AAAA,MAC/B;AAKA,eAAS,OAAO,OAAO;AACrB,SAAC,MAAM,UAAU,UAAU,OAAO,OAAO,MAAM,IAAI;AAAA,MACrD;AAEA,aAAO,SAAS,UAAU,YAAY,QAAQ;AAC5C,YAAI,SAAS,aAAa;AAC1B,kBAAU,WAAW,MAAM,UAAU,WAAW,OAAO,CAAC,EAAE,MAAM,CAAC;AACjE,oBAAY,OAAO,CAAC,EAAE;AACtB,YAAI,WAAW,UAAU;AAOvB,oBAAU,QAAQ,EAAE,QAAQ,KAAK;AACjC,aAAG;AACD,mBAAO,OAAO,OAAO,GAAG,CAAC,EAAE,CAAC,CAAC;AAC7B,qBAAS,aAAa;AAAA,UACxB,SAAS,WAAW,YAAY,OAAO,UAAU,OAAO,CAAC,EAAE,WAAW;AACtE,oBAAU,QAAQ,EAAE,QAAQ,IAAI;AAAA,QAClC,OAAO;AACL,cAAI,OAAO,CAAC,EAAE,UAAU,SAAS;AAC/B,sBAAU,KAAK,OAAO,CAAC,EAAE,IAAI;AAAA,UAC/B,OAAO;AACL,sBAAU,IAAI;AAAA,UAChB;AACA,iBAAO,OAAO,OAAO,GAAG,CAAC,EAAE,CAAC,CAAC;AAAA,QAC/B;AAAA,MACF;AACA,aAAO,SAAS,WAAW,MAAM,OAAO,SAAS,CAAC;AAAA,IACpD;AAYA,QAAM,mBAAmB,CAAC;AAK1B,QAAM,QAAQ,CAAC,YAAY;AACzB,cAAQ,MAAM,OAAO;AAAA,IACvB;AAMA,QAAM,OAAO,CAAC,YAAY,SAAS;AACjC,cAAQ,IAAI,SAAS,OAAO,IAAI,GAAG,IAAI;AAAA,IACzC;AAMA,QAAM,aAAa,CAACC,UAAS,YAAY;AACvC,UAAI,iBAAiB,GAAGA,QAAO,IAAI,OAAO,EAAE,EAAG;AAE/C,cAAQ,IAAI,oBAAoBA,QAAO,KAAK,OAAO,EAAE;AACrD,uBAAiB,GAAGA,QAAO,IAAI,OAAO,EAAE,IAAI;AAAA,IAC9C;AAOA,QAAM,WAAW;AACjB,QAAM,YAAY;AAClB,QAAM,WAAW,OAAO,SAAS;AAMjC,QAAM,OAAO,SAAS,MAAM;AAG1B,YAAM,YAAY,uBAAO,OAAO,IAAI;AAEpC,YAAM,UAAU,uBAAO,OAAO,IAAI;AAElC,YAAM,UAAU,CAAC;AAIjB,UAAI,YAAY;AAChB,YAAM,cAAc;AACpB,YAAM,qBAAqB;AAE3B,YAAM,qBAAqB,EAAE,mBAAmB,MAAM,MAAM,cAAc,UAAU,CAAC,EAAE;AAKvF,UAAI,UAAU;AAAA,QACZ,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,YAAY;AAAA,QACZ,OAAO;AAAA,QACP,WAAW;AAAA;AAAA;AAAA,QAGX,WAAW;AAAA,MACb;AAQA,eAAS,mBAAmB,cAAc;AACxC,eAAO,QAAQ,cAAc,KAAK,YAAY;AAAA,MAChD;AAKA,eAAS,cAAc,OAAO;AAC5B,YAAI,UAAU,MAAM,YAAY;AAEhC,mBAAW,MAAM,aAAa,MAAM,WAAW,YAAY;AAG3D,cAAM,QAAQ,QAAQ,iBAAiB,KAAK,OAAO;AACnD,YAAI,OAAO;AACT,gBAAM,WAAW,YAAY,MAAM,CAAC,CAAC;AACrC,cAAI,CAAC,UAAU;AACb,iBAAK,mBAAmB,QAAQ,MAAM,MAAM,CAAC,CAAC,CAAC;AAC/C,iBAAK,qDAAqD,KAAK;AAAA,UACjE;AACA,iBAAO,WAAW,MAAM,CAAC,IAAI;AAAA,QAC/B;AAEA,eAAO,QACJ,MAAM,KAAK,EACX,KAAK,CAAC,WAAW,mBAAmB,MAAM,KAAK,YAAY,MAAM,CAAC;AAAA,MACvE;AAwBA,eAASC,WAAU,oBAAoB,eAAe,gBAAgB,cAAc;AAClF,YAAI,OAAO;AACX,YAAI,eAAe;AACnB,YAAI,OAAO,kBAAkB,UAAU;AACrC,iBAAO;AACP,2BAAiB,cAAc;AAC/B,yBAAe,cAAc;AAG7B,yBAAe;AAAA,QACjB,OAAO;AAEL,qBAAW,UAAU,qDAAqD;AAC1E,qBAAW,UAAU,uGAAuG;AAC5H,yBAAe;AACf,iBAAO;AAAA,QACT;AAGA,cAAM,UAAU;AAAA,UACd;AAAA,UACA,UAAU;AAAA,QACZ;AAGA,aAAK,oBAAoB,OAAO;AAIhC,cAAM,SAAS,QAAQ,SACnB,QAAQ,SACR,WAAW,QAAQ,UAAU,QAAQ,MAAM,gBAAgB,YAAY;AAE3E,eAAO,OAAO,QAAQ;AAEtB,aAAK,mBAAmB,MAAM;AAE9B,eAAO;AAAA,MACT;AAWA,eAAS,WAAW,cAAc,iBAAiB,gBAAgB,cAAc;AAO/E,iBAAS,YAAY,MAAM,OAAO;AAChC,gBAAM,YAAY,SAAS,mBAAmB,MAAM,CAAC,EAAE,YAAY,IAAI,MAAM,CAAC;AAC9E,iBAAO,OAAO,UAAU,eAAe,KAAK,KAAK,UAAU,SAAS,KAAK,KAAK,SAAS,SAAS;AAAA,QAClG;AAEA,iBAAS,kBAAkB;AACzB,cAAI,CAAC,IAAI,UAAU;AACjB,oBAAQ,QAAQ,UAAU;AAC1B;AAAA,UACF;AAEA,cAAI,YAAY;AAChB,cAAI,iBAAiB,YAAY;AACjC,cAAI,QAAQ,IAAI,iBAAiB,KAAK,UAAU;AAChD,cAAI,MAAM;AAEV,iBAAO,OAAO;AACZ,mBAAO,WAAW,UAAU,WAAW,MAAM,KAAK;AAClD,kBAAM,OAAO,YAAY,KAAK,KAAK;AACnC,gBAAI,MAAM;AACR,oBAAM,CAAC,MAAM,gBAAgB,IAAI;AACjC,sBAAQ,QAAQ,GAAG;AACnB,oBAAM;AAEN,2BAAa;AACb,kBAAI,KAAK,WAAW,GAAG,GAAG;AAGxB,uBAAO,MAAM,CAAC;AAAA,cAChB,OAAO;AACL,sBAAM,WAAW,SAAS,iBAAiB,IAAI,KAAK;AACpD,wBAAQ,WAAW,MAAM,CAAC,GAAG,QAAQ;AAAA,cACvC;AAAA,YACF,OAAO;AACL,qBAAO,MAAM,CAAC;AAAA,YAChB;AACA,wBAAY,IAAI,iBAAiB;AACjC,oBAAQ,IAAI,iBAAiB,KAAK,UAAU;AAAA,UAC9C;AACA,iBAAO,WAAW,OAAO,SAAS;AAClC,kBAAQ,QAAQ,GAAG;AAAA,QACrB;AAEA,iBAAS,qBAAqB;AAC5B,cAAI,eAAe,GAAI;AAEvB,cAAIC,UAAS;AAEb,cAAI,OAAO,IAAI,gBAAgB,UAAU;AACvC,gBAAI,CAAC,UAAU,IAAI,WAAW,GAAG;AAC/B,sBAAQ,QAAQ,UAAU;AAC1B;AAAA,YACF;AACA,YAAAA,UAAS,WAAW,IAAI,aAAa,YAAY,MAAM,cAAc,IAAI,WAAW,CAAC;AACrF,0BAAc,IAAI,WAAW;AAAA,YAAiCA,QAAO;AAAA,UACvE,OAAO;AACL,YAAAA,UAAS,cAAc,YAAY,IAAI,YAAY,SAAS,IAAI,cAAc,IAAI;AAAA,UACpF;AAMA,cAAI,IAAI,YAAY,GAAG;AACrB,yBAAaA,QAAO;AAAA,UACtB;AACA,kBAAQ,eAAeA,QAAO,SAASA,QAAO,QAAQ;AAAA,QACxD;AAEA,iBAAS,gBAAgB;AACvB,cAAI,IAAI,eAAe,MAAM;AAC3B,+BAAmB;AAAA,UACrB,OAAO;AACL,4BAAgB;AAAA,UAClB;AACA,uBAAa;AAAA,QACf;AAKA,iBAAS,aAAa,MAAM;AAC1B,cAAI,KAAK,WAAW;AAClB,oBAAQ,SAAS,SAAS,iBAAiB,KAAK,SAAS,KAAK,KAAK,SAAS;AAAA,UAC9E;AACA,gBAAM,OAAO,OAAO,MAAM,EAAE,QAAQ,EAAE,OAAO,IAAI,EAAE,CAAC;AACpD,iBAAO;AAAA,QACT;AAQA,iBAAS,UAAU,MAAM,OAAO,oBAAoB;AAClD,cAAI,UAAU,WAAW,KAAK,OAAO,kBAAkB;AAEvD,cAAI,SAAS;AACX,gBAAI,KAAK,QAAQ,GAAG;AAClB,oBAAM,OAAO,IAAI,SAAS,IAAI;AAC9B,mBAAK,QAAQ,EAAE,OAAO,IAAI;AAC1B,kBAAI,KAAK,eAAgB,WAAU;AAAA,YACrC;AAEA,gBAAI,SAAS;AACX,qBAAO,KAAK,cAAc,KAAK,QAAQ;AACrC,uBAAO,KAAK;AAAA,cACd;AACA,qBAAO;AAAA,YACT;AAAA,UACF;AAGA,cAAI,KAAK,gBAAgB;AACvB,mBAAO,UAAU,KAAK,QAAQ,OAAO,kBAAkB;AAAA,UACzD;AAAA,QACF;AAOA,iBAAS,SAAS,QAAQ;AACxB,cAAI,IAAI,QAAQ,eAAe,GAAG;AAGhC,0BAAc,OAAO,CAAC;AACtB,mBAAO;AAAA,UACT,OAAO;AAGL,uCAA2B;AAC3B,mBAAO;AAAA,UACT;AAAA,QACF;AAQA,iBAAS,aAAa,OAAO;AAC3B,gBAAM,SAAS,MAAM,CAAC;AACtB,gBAAM,UAAU,MAAM;AAEtB,gBAAM,OAAO,IAAI,SAAS,OAAO;AAEjC,gBAAM,kBAAkB,CAAC,QAAQ,eAAe,QAAQ,UAAU,CAAC;AACnE,qBAAW,MAAM,iBAAiB;AAChC,gBAAI,CAAC,GAAI;AACT,eAAG,OAAO,IAAI;AACd,gBAAI,KAAK,eAAgB,QAAO,SAAS,MAAM;AAAA,UACjD;AAEA,cAAI,WAAW,QAAQ,gBAAgB;AACrC,oBAAQ,QAAQ,OAAO,MAAM;AAAA,UAC/B;AAEA,cAAI,QAAQ,MAAM;AAChB,0BAAc;AAAA,UAChB,OAAO;AACL,gBAAI,QAAQ,cAAc;AACxB,4BAAc;AAAA,YAChB;AACA,0BAAc;AACd,gBAAI,CAAC,QAAQ,eAAe,CAAC,QAAQ,cAAc;AACjD,2BAAa;AAAA,YACf;AAAA,UACF;AACA,uBAAa,OAAO;AAKpB,iBAAO,QAAQ,cAAc,IAAI,OAAO;AAAA,QAC1C;AAOA,iBAAS,WAAW,OAAO;AACzB,gBAAM,SAAS,MAAM,CAAC;AACtB,gBAAM,qBAAqB,gBAAgB,OAAO,MAAM,KAAK;AAE7D,gBAAM,UAAU,UAAU,KAAK,OAAO,kBAAkB;AACxD,cAAI,CAAC,SAAS;AAAE,mBAAO;AAAA,UAAU;AAEjC,gBAAM,SAAS;AACf,cAAI,OAAO,MAAM;AACf,0BAAc;AAAA,UAChB,OAAO;AACL,gBAAI,EAAE,OAAO,aAAa,OAAO,aAAa;AAC5C,4BAAc;AAAA,YAChB;AACA,0BAAc;AACd,gBAAI,OAAO,YAAY;AACrB,2BAAa;AAAA,YACf;AAAA,UACF;AACA,aAAG;AACD,gBAAI,IAAI,WAAW;AACjB,sBAAQ,UAAU;AAAA,YACpB;AACA,gBAAI,CAAC,IAAI,QAAQ,CAAC,IAAI,aAAa;AACjC,2BAAa,IAAI;AAAA,YACnB;AACA,kBAAM,IAAI;AAAA,UACZ,SAAS,QAAQ,QAAQ;AACzB,cAAI,QAAQ,QAAQ;AAClB,gBAAI,QAAQ,gBAAgB;AAC1B,sBAAQ,OAAO,QAAQ,QAAQ;AAAA,YACjC;AACA,yBAAa,QAAQ,MAAM;AAAA,UAC7B;AACA,iBAAO,OAAO,YAAY,IAAI,OAAO;AAAA,QACvC;AAEA,iBAAS,uBAAuB;AAC9B,gBAAM,OAAO,CAAC;AACd,mBAAS,UAAU,KAAK,YAAY,UAAU,UAAU,QAAQ,QAAQ;AACtE,gBAAI,QAAQ,WAAW;AACrB,mBAAK,QAAQ,QAAQ,SAAS;AAAA,YAChC;AAAA,UACF;AACA,eAAK,QAAQ,UAAQ,QAAQ,SAAS,IAAI,CAAC;AAAA,QAC7C;AAGA,YAAI,YAAY,CAAC;AAQjB,iBAAS,cAAc,iBAAiB,OAAO;AAC7C,gBAAM,SAAS,SAAS,MAAM,CAAC;AAG/B,wBAAc;AAEd,cAAI,UAAU,MAAM;AAClB,0BAAc;AACd,mBAAO;AAAA,UACT;AAMA,cAAI,UAAU,SAAS,WAAW,MAAM,SAAS,SAAS,UAAU,UAAU,MAAM,SAAS,WAAW,IAAI;AAE1G,0BAAc,gBAAgB,MAAM,MAAM,OAAO,MAAM,QAAQ,CAAC;AAChE,gBAAI,CAAC,WAAW;AAEd,oBAAM,MAAM,IAAI,MAAM,qBAAqB;AAC3C,kBAAI,eAAe;AACnB,kBAAI,UAAU,UAAU;AACxB,oBAAM;AAAA,YACR;AACA,mBAAO;AAAA,UACT;AACA,sBAAY;AAEZ,cAAI,MAAM,SAAS,SAAS;AAC1B,mBAAO,aAAa,KAAK;AAAA,UAC3B,WAAW,MAAM,SAAS,aAAa,CAAC,gBAAgB;AAGtD,kBAAM,MAAM,IAAI,MAAM,qBAAqB,SAAS,kBAAkB,IAAI,aAAa,eAAe,GAAG;AACzG,gBAAI,OAAO;AACX,kBAAM;AAAA,UACR,WAAW,MAAM,SAAS,OAAO;AAC/B,kBAAM,YAAY,WAAW,KAAK;AAClC,gBAAI,cAAc,UAAU;AAC1B,qBAAO;AAAA,YACT;AAAA,UACF;AAKA,cAAI,MAAM,SAAS,aAAa,WAAW,IAAI;AAE7C,mBAAO;AAAA,UACT;AAMA,cAAI,aAAa,OAAU,aAAa,MAAM,QAAQ,GAAG;AACvD,kBAAM,MAAM,IAAI,MAAM,2DAA2D;AACjF,kBAAM;AAAA,UACR;AAcA,wBAAc;AACd,iBAAO,OAAO;AAAA,QAChB;AAEA,cAAM,WAAW,YAAY,YAAY;AACzC,YAAI,CAAC,UAAU;AACb,gBAAM,mBAAmB,QAAQ,MAAM,YAAY,CAAC;AACpD,gBAAM,IAAI,MAAM,wBAAwB,eAAe,GAAG;AAAA,QAC5D;AAEA,cAAM,KAAK,gBAAgB,UAAU,EAAE,QAAQ,CAAC;AAChD,YAAI,SAAS;AAEb,YAAI,MAAM,gBAAgB;AAE1B,cAAM,gBAAgB,CAAC;AACvB,cAAM,UAAU,IAAI,QAAQ,UAAU,OAAO;AAC7C,6BAAqB;AACrB,YAAI,aAAa;AACjB,YAAI,YAAY;AAChB,YAAI,QAAQ;AACZ,YAAI,aAAa;AACjB,YAAI,2BAA2B;AAE/B,YAAI;AACF,cAAI,QAAQ,YAAY;AAExB,qBAAS;AACP;AACA,gBAAI,0BAA0B;AAG5B,yCAA2B;AAAA,YAC7B,OAAO;AACL,kBAAI,QAAQ,YAAY;AAAA,YAC1B;AACA,gBAAI,QAAQ,YAAY;AAExB,kBAAM,QAAQ,IAAI,QAAQ,KAAK,eAAe;AAG9C,gBAAI,CAAC,MAAO;AAEZ,kBAAM,cAAc,gBAAgB,UAAU,OAAO,MAAM,KAAK;AAChE,kBAAM,iBAAiB,cAAc,aAAa,KAAK;AACvD,oBAAQ,MAAM,QAAQ;AAAA,UACxB;AACA,wBAAc,gBAAgB,OAAO,KAAK,CAAC;AAC3C,kBAAQ,cAAc;AACtB,kBAAQ,SAAS;AACjB,mBAAS,QAAQ,OAAO;AAExB,iBAAO;AAAA;AAAA;AAAA,YAGL,WAAW,KAAK,MAAM,SAAS;AAAA,YAC/B,OAAO;AAAA,YACP,UAAU;AAAA,YACV,SAAS;AAAA,YACT;AAAA,YACA;AAAA,UACF;AAAA,QACF,SAAS,KAAK;AACZ,cAAI,IAAI,WAAW,IAAI,QAAQ,SAAS,SAAS,GAAG;AAClD,mBAAO;AAAA,cACL,SAAS;AAAA,cACT,WAAW;AAAA,gBACT,KAAK,IAAI;AAAA,gBACT,SAAS,gBAAgB,MAAM,QAAQ,KAAK,QAAQ,GAAG;AAAA,gBACvD,MAAM,IAAI;AAAA,cACZ;AAAA,cACA,OAAO;AAAA,cACP,WAAW;AAAA,cACX,OAAO,SAAS,eAAe;AAAA,cAC/B;AAAA,YACF;AAAA,UACF,WAAW,WAAW;AACpB,mBAAO;AAAA,cACL,SAAS;AAAA,cACT,WAAW;AAAA,cACX,OAAO,SAAS,eAAe;AAAA,cAC/B;AAAA,cACA,UAAU;AAAA,cACV;AAAA,cACA,aAAa;AAAA,YACf;AAAA,UACF,OAAO;AACL,kBAAM;AAAA,UACR;AAAA,QACF;AAAA,MACF;AASA,eAAS,wBAAwB,MAAM;AACrC,cAAM,SAAS;AAAA,UACb,WAAW;AAAA,UACX,SAAS,IAAI,QAAQ,UAAU,OAAO;AAAA,UACtC,OAAO,SAAS,IAAI;AAAA,UACpB,SAAS;AAAA,UACT,KAAK;AAAA,QACP;AACA,eAAO,QAAQ,QAAQ,IAAI;AAC3B,eAAO;AAAA,MACT;AAgBA,eAAS,cAAc,MAAM,gBAAgB;AAC3C,yBAAiB,kBAAkB,QAAQ,aAAa,OAAO,KAAK,SAAS;AAC7E,cAAM,YAAY,wBAAwB,IAAI;AAE9C,cAAM,UAAU,eAAe,OAAO,WAAW,EAAE,OAAO,aAAa,EAAE;AAAA,UAAI,UAC3E,WAAW,MAAM,MAAM,KAAK;AAAA,QAC9B;AACA,gBAAQ,QAAQ,SAAS;AAEzB,cAAM,SAAS,QAAQ,KAAK,CAAC,GAAG,MAAM;AAEpC,cAAI,EAAE,cAAc,EAAE,UAAW,QAAO,EAAE,YAAY,EAAE;AAIxD,cAAI,EAAE,YAAY,EAAE,UAAU;AAC5B,gBAAI,YAAY,EAAE,QAAQ,EAAE,eAAe,EAAE,UAAU;AACrD,qBAAO;AAAA,YACT,WAAW,YAAY,EAAE,QAAQ,EAAE,eAAe,EAAE,UAAU;AAC5D,qBAAO;AAAA,YACT;AAAA,UACF;AAMA,iBAAO;AAAA,QACT,CAAC;AAED,cAAM,CAAC,MAAM,UAAU,IAAI;AAG3B,cAAM,SAAS;AACf,eAAO,cAAc;AAErB,eAAO;AAAA,MACT;AAWA,eAAS,UAAU,MAAM;AACvB,YAAI,EAAE,QAAQ,cAAc,QAAQ,QAAQ;AAC1C,iBAAO;AAAA,QACT;AAEA,eAAO,KAAK,QAAQ,aAAa,WAAS;AACxC,cAAI,UAAU,MAAM;AAClB,mBAAO,QAAQ,QAAQ,SAAS;AAAA,UAClC,WAAW,QAAQ,YAAY;AAC7B,mBAAO,MAAM,QAAQ,OAAO,QAAQ,UAAU;AAAA,UAChD;AACA,iBAAO;AAAA,QACT,CAAC;AAAA,MACH;AASA,eAAS,gBAAgB,SAAS,aAAa,YAAY;AACzD,cAAM,WAAW,cAAc,QAAQ,WAAW,IAAI;AAEtD,gBAAQ,UAAU,IAAI,MAAM;AAC5B,YAAI,SAAU,SAAQ,UAAU,IAAI,QAAQ;AAAA,MAC9C;AAGA,YAAM,WAAW;AAAA,QACf,2BAA2B,CAAC,EAAE,GAAG,MAAM;AACrC,cAAI,QAAQ,OAAO;AACjB,eAAG,YAAY,GAAG,UAAU,QAAQ,OAAO,EAAE,EAAE,QAAQ,cAAc,IAAI;AAAA,UAC3E;AAAA,QACF;AAAA,QACA,0BAA0B,CAAC,EAAE,OAAO,MAAM;AACxC,cAAI,QAAQ,OAAO;AACjB,mBAAO,QAAQ,OAAO,MAAM,QAAQ,OAAO,MAAM;AAAA,UACnD;AAAA,QACF;AAAA,MACF;AAEA,YAAM,iBAAiB;AAEvB,YAAM,mBAAmB;AAAA,QACvB,0BAA0B,CAAC,EAAE,OAAO,MAAM;AACxC,cAAI,QAAQ,YAAY;AACtB,mBAAO,QAAQ,OAAO,MAAM;AAAA,cAAQ;AAAA,cAAgB,CAAC,MACnD,EAAE,QAAQ,OAAO,QAAQ,UAAU;AAAA,YACrC;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAQA,eAAS,iBAAiB,SAAS;AAEjC,YAAI,OAAO;AACX,cAAM,WAAW,cAAc,OAAO;AAEtC,YAAI,mBAAmB,QAAQ,EAAG;AAGlC;AAAA,UAAK;AAAA,UACH,EAAE,IAAI,SAAS,SAAmB;AAAA,QAAC;AAErC,eAAO;AACP,cAAM,OAAO,KAAK;AAClB,cAAM,SAAS,WAAWD,WAAU,MAAM,EAAE,UAAU,gBAAgB,KAAK,CAAC,IAAI,cAAc,IAAI;AAGlG,aAAK,0BAA0B,EAAE,IAAI,SAAS,QAAQ,KAAK,CAAC;AAE5D,gBAAQ,YAAY,OAAO;AAC3B,wBAAgB,SAAS,UAAU,OAAO,QAAQ;AAClD,gBAAQ,SAAS;AAAA,UACf,UAAU,OAAO;AAAA;AAAA,UAEjB,IAAI,OAAO;AAAA,UACX,WAAW,OAAO;AAAA,QACpB;AACA,YAAI,OAAO,aAAa;AACtB,kBAAQ,cAAc;AAAA,YACpB,UAAU,OAAO,YAAY;AAAA;AAAA,YAE7B,IAAI,OAAO,YAAY;AAAA,YACvB,WAAW,OAAO,YAAY;AAAA,UAChC;AAAA,QACF;AAAA,MACF;AAOA,eAAS,UAAU,aAAa;AAC9B,YAAI,YAAY,OAAO;AACrB,qBAAW,UAAU,2CAA2C;AAChE,qBAAW,UAAU,oEAAoE;AAAA,QAC3F;AACA,kBAAU,UAAU,SAAS,WAAW;AAAA,MAC1C;AAQA,YAAM,mBAAmB,MAAM;AAC7B,YAAI,iBAAiB,OAAQ;AAC7B,yBAAiB,SAAS;AAE1B,mBAAW,UAAU,gEAAgE;AAErF,cAAM,SAAS,SAAS,iBAAiB,UAAU;AACnD,eAAO,QAAQ,gBAAgB;AAAA,MACjC;AAIA,eAAS,yBAAyB;AAChC,mBAAW,UAAU,sEAAsE;AAC3F,yBAAiB;AAAA,MACnB;AAEA,UAAI,iBAAiB;AAKrB,eAAS,eAAe;AAEtB,YAAI,SAAS,eAAe,WAAW;AACrC,2BAAiB;AACjB;AAAA,QACF;AAEA,cAAM,SAAS,SAAS,iBAAiB,UAAU;AACnD,eAAO,QAAQ,gBAAgB;AAAA,MACjC;AAEA,eAAS,OAAO;AAEd,YAAI,eAAgB,cAAa;AAAA,MACnC;AAGA,UAAI,OAAO,WAAW,eAAe,OAAO,kBAAkB;AAC5D,eAAO,iBAAiB,oBAAoB,MAAM,KAAK;AAAA,MACzD;AAQA,eAAS,iBAAiB,cAAc,oBAAoB;AAC1D,YAAI,OAAO;AACX,YAAI;AACF,iBAAO,mBAAmB,IAAI;AAAA,QAChC,SAAS,SAAS;AAChB,gBAAM,wDAAwD,QAAQ,MAAM,YAAY,CAAC;AAEzF,cAAI,CAAC,WAAW;AAAE,kBAAM;AAAA,UAAS,OAAO;AAAE,kBAAM,OAAO;AAAA,UAAG;AAK1D,iBAAO;AAAA,QACT;AAEA,YAAI,CAAC,KAAK,KAAM,MAAK,OAAO;AAC5B,kBAAU,YAAY,IAAI;AAC1B,aAAK,gBAAgB,mBAAmB,KAAK,MAAM,IAAI;AAEvD,YAAI,KAAK,SAAS;AAChB,0BAAgB,KAAK,SAAS,EAAE,aAAa,CAAC;AAAA,QAChD;AAAA,MACF;AAOA,eAAS,mBAAmB,cAAc;AACxC,eAAO,UAAU,YAAY;AAC7B,mBAAW,SAAS,OAAO,KAAK,OAAO,GAAG;AACxC,cAAI,QAAQ,KAAK,MAAM,cAAc;AACnC,mBAAO,QAAQ,KAAK;AAAA,UACtB;AAAA,QACF;AAAA,MACF;AAKA,eAAS,gBAAgB;AACvB,eAAO,OAAO,KAAK,SAAS;AAAA,MAC9B;AAWA,eAAS,gBAAgB,MAAM;AAC7B,mBAAW,UAAU,kDAAkD;AACvE,mBAAW,UAAU,kEAAkE;AAEvF,cAAM,OAAO,YAAY,IAAI;AAC7B,YAAI,MAAM;AAAE,iBAAO;AAAA,QAAM;AAEzB,cAAM,MAAM,IAAI,MAAM,iDAAmD,QAAQ,MAAM,IAAI,CAAC;AAC5F,cAAM;AAAA,MACR;AAMA,eAAS,YAAY,MAAM;AACzB,gBAAQ,QAAQ,IAAI,YAAY;AAChC,eAAO,UAAU,IAAI,KAAK,UAAU,QAAQ,IAAI,CAAC;AAAA,MACnD;AAOA,eAAS,gBAAgB,WAAW,EAAE,aAAa,GAAG;AACpD,YAAI,OAAO,cAAc,UAAU;AACjC,sBAAY,CAAC,SAAS;AAAA,QACxB;AACA,kBAAU,QAAQ,WAAS;AAAE,kBAAQ,MAAM,YAAY,CAAC,IAAI;AAAA,QAAc,CAAC;AAAA,MAC7E;AAMA,eAAS,cAAc,MAAM;AAC3B,cAAM,OAAO,YAAY,IAAI;AAC7B,eAAO,QAAQ,CAAC,KAAK;AAAA,MACvB;AAOA,eAAS,iBAAiB,QAAQ;AAEhC,YAAI,OAAO,uBAAuB,KAAK,CAAC,OAAO,yBAAyB,GAAG;AACzE,iBAAO,yBAAyB,IAAI,CAAC,SAAS;AAC5C,mBAAO,uBAAuB;AAAA,cAC5B,OAAO,OAAO,EAAE,OAAO,KAAK,GAAG,GAAG,IAAI;AAAA,YACxC;AAAA,UACF;AAAA,QACF;AACA,YAAI,OAAO,sBAAsB,KAAK,CAAC,OAAO,wBAAwB,GAAG;AACvE,iBAAO,wBAAwB,IAAI,CAAC,SAAS;AAC3C,mBAAO,sBAAsB;AAAA,cAC3B,OAAO,OAAO,EAAE,OAAO,KAAK,GAAG,GAAG,IAAI;AAAA,YACxC;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAKA,eAAS,UAAU,QAAQ;AACzB,yBAAiB,MAAM;AACvB,gBAAQ,KAAK,MAAM;AAAA,MACrB;AAOA,eAAS,KAAK,OAAO,MAAM;AACzB,cAAM,KAAK;AACX,gBAAQ,QAAQ,SAAS,QAAQ;AAC/B,cAAI,OAAO,EAAE,GAAG;AACd,mBAAO,EAAE,EAAE,IAAI;AAAA,UACjB;AAAA,QACF,CAAC;AAAA,MACH;AAQA,eAAS,mBAAmB,KAAK;AAC/B,mBAAW,UAAU,6CAA6C;AAClE,mBAAW,UAAU,oEAAoE;AAEzF,eAAO,UAAU,GAAG;AAAA,MACtB;AAMA,eAAS,wBAAwB,IAAI;AACnC,mBAAW,UAAU,kDAAkD;AACvE,mBAAW,UAAU,kCAAkC;AAEvD,eAAO,iBAAiB,EAAE;AAAA,MAC5B;AAGA,aAAO,OAAO,MAAM;AAAA,QAClB,WAAAA;AAAA,QACA;AAAA,QACA;AAAA,QACA,WAAW;AAAA,QACX;AAAA;AAAA,QAEA,gBAAgB;AAAA,QAChB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,SAAS;AAAA,QACT;AAAA;AAAA,QAEA,WAAW,eAAe,IAAI,EAAE;AAAA,MAClC,CAAC;AAED,WAAK,YAAY,WAAW;AAAE,oBAAY;AAAA,MAAO;AACjD,WAAK,WAAW,WAAW;AAAE,oBAAY;AAAA,MAAM;AAC/C,WAAK,gBAAgB;AAErB,iBAAW,OAAO,OAAO;AAEvB,YAAI,OAAO,MAAM,GAAG,MAAM,UAAU;AAElC,wBAAc,MAAM,GAAG,CAAC;AAAA,QAC1B;AAAA,MACF;AAGA,aAAO,OAAO,MAAM,KAAK;AAGzB,WAAK,UAAU,QAAQ;AACvB,WAAK,UAAU,eAAe;AAC9B,WAAK,UAAU,gBAAgB;AAC/B,aAAO;AAAA,IACT;AAGA,QAAI,YAAY,KAAK,CAAC,CAAC;AAEvB,WAAO,UAAU;AAAA;AAAA;;;ACp9EjB;AAAA;AAWC,KAAC,WAAW;AAGX,UAAI;AAGJ,UAAI,OAAO,WAAW,aAAa;AACjC,oBAAY,OAAO,UAAU;AAAA,MAC/B,OAGK;AAEH,oBAAa,WAAU;AAAE,iBAAO,SAAS,GAAE,MAAM,MAAM;AAAA,QAAE,EAAE;AAAA,MAC7D;AAEA,gBAAU,SAAS;AACnB,gBAAU,WAAW;AAErB,UAAI,OAAO,YAAY,eAAe,OAAO,QAAQ,QAAQ,YAAY;AACvE,kBAAU,SAAS;AAAA,MACrB;AAEA,eAAS,SAAkB;AACzB,gBAAQ,IAAI,OAAO,MAAM,MAAM,SAAS,CAAC;AAAA,MAC3C;AAEA,eAAS,SAAS,KAAK,cAAc;AACnC,eAAO,OAAO,MAAM,MAAM,CAAC,GAAG,EAAE,OAAO,YAAY,CAAC;AAAA,MACtD;AAEA,eAAS,OAAO,KAAK;AACnB,YAAI,WAAW,GACX,OAAO,CAAC,EAAE,MAAM,KAAK,SAAS,GAC9B,IAAI,GACJ,IAAI,IAAI,QACR,SAAS,IACT,GACA,UAAU,OACV,KACA,KACA,cAAc,OACd,WACA,UAAU,WAAW;AAAE,iBAAO,KAAK,UAAU;AAAA,QAAG,GAChD,cAAc,WAAW;AACvB,cAAI,SAAS;AACb,iBAAO,KAAK,KAAK,IAAI,CAAC,CAAC,GAAG;AACxB,sBAAU,IAAI,GAAG;AACjB,gBAAI,IAAI,CAAC;AAAA,UACX;AACA,iBAAO,OAAO,SAAS,IAAI,SAAS,MAAM,IAAI;AAAA,QAChD;AAEJ,eAAO,IAAI,GAAG,EAAE,GAAG;AACjB,cAAI,IAAI,CAAC;AACT,cAAI,SAAS;AACX,sBAAU;AACV,gBAAI,KAAK,KAAK;AACZ,4BAAc;AACd,kBAAI,IAAI,EAAE,CAAC;AAAA,YACb,WACS,KAAK,OAAO,IAAI,IAAI,CAAC,KAAK,KAAK;AACtC,4BAAc;AACd,mBAAK;AACL,kBAAI,IAAI,CAAC;AAAA,YACX,OACK;AACH,4BAAc;AAAA,YAChB;AACA,wBAAY,YAAY;AACxB,oBAAQ,GAAG;AAAA,cACX,KAAK;AACH,0BAAU,SAAS,QAAQ,GAAG,EAAE,EAAE,SAAS,CAAC;AAC5C;AAAA,cACF,KAAK;AACH,sBAAM,QAAQ;AACd,oBAAI,OAAO,QAAQ,YAAY,eAAe;AAC5C,4BAAU;AAAA;AAEV,4BAAU,OAAO,aAAa,SAAS,KAAK,EAAE,CAAC;AACjD;AAAA,cACF,KAAK;AACH,0BAAU,SAAS,QAAQ,GAAG,EAAE;AAChC;AAAA,cACF,KAAK;AACH,sBAAM,OAAO,WAAW,QAAQ,CAAC,EAAE,QAAQ,aAAa,CAAC,CAAC;AAC1D,0BAAU,cAAc,MAAM,IAAI,QAAQ,MAAM,EAAE;AAClD;AAAA,cACF,KAAK;AACH,0BAAU,KAAK,UAAU,QAAQ,CAAC;AAClC;AAAA,cACF,KAAK;AACH,0BAAU,MAAM,SAAS,QAAQ,GAAG,EAAE,EAAE,SAAS,CAAC;AAClD;AAAA,cACF,KAAK;AACH,0BAAU,QAAQ;AAClB;AAAA,cACF,KAAK;AACH,0BAAU,OAAO,SAAS,QAAQ,GAAG,EAAE,EAAE,SAAS,EAAE;AACpD;AAAA,cACF,KAAK;AACH,0BAAU,OAAO,SAAS,QAAQ,GAAG,EAAE,EAAE,SAAS,EAAE,EAAE,YAAY;AAClE;AAAA,cACF;AACE,0BAAU;AACV;AAAA,YACF;AAAA,UACF,WAAW,MAAM,KAAK;AACpB,sBAAU;AAAA,UACZ,OAAO;AACL,sBAAU;AAAA,UACZ;AAAA,QACF;AACA,eAAO;AAAA,MACT;AAAA,IAEF,GAAE;AAAA;AAAA;;;AC/HF;AAAA;AAAA;AAEA,QAAI,YAAY;AAEhB,QAAI,QAAQ,OAAO,KAAK;AAExB,WAAO,UAAU;AAEjB,UAAM,OAAO,OAAO,SAAS;AAC7B,UAAM,QAAQ,OAAO,UAAU;AAC/B,UAAM,YAAY,OAAO,cAAc;AACvC,UAAM,SAAS,OAAO,WAAW;AACjC,UAAM,OAAO,OAAO,SAAS;AAC7B,UAAM,MAAM,OAAO,QAAQ;AAE3B,UAAM,SAAS;AAGf,aAAS,OAAO,cAAc;AAC5B,qBAAe,cAAc,aAAa,eAAe,aAAa;AAEtE,aAAO;AAEP,eAAS,eAAe,QAAQ;AAC9B,YAAI,QAAQ;AACV,mBAAS,UAAU,MAAM,MAAM,SAAS;AAAA,QAC1C;AAEA,eAAO,IAAI,aAAa,MAAM;AAAA,MAChC;AAAA,IACF;AAAA;AAAA;;;AC9BA,IAAAE,gBAAA;AAAA;AAEA,QAAI,OAAO;AACX,QAAI,QAAQ;AAEZ,YAAQ,YAAY;AACpB,YAAQ,gBAAgB;AACxB,YAAQ,mBAAmB;AAC3B,YAAQ,gBAAgB;AACxB,YAAQ,gBAAgB;AAExB,YAAQ,UAAU,UAAU;AAC5B,YAAQ,UAAU,aAAa;AAC/B,YAAQ,UAAU,iBAAiB;AACnC,YAAQ,UAAU,WAAW;AAC7B,YAAQ,UAAU,YAAY;AAC9B,YAAQ,UAAU,gBAAgB;AAClC,YAAQ,UAAU,WAAW;AAC7B,YAAQ,UAAU,SAAS;AAE3B,QAAI,gBAAgB;AAGpB,aAAS,UAAU,MAAM,OAAO,SAAS;AACvC,UAAI,SAAS,KAAK,UAAU,CAAC,CAAC;AAC9B,UAAI,WAAW,WAAW,CAAC;AAC3B,UAAI,SAAS,SAAS;AACtB,UAAI;AAEJ,UAAI,OAAO,SAAS,UAAU;AAC5B,cAAM,MAAM,wCAAwC,IAAI;AAAA,MAC1D;AAEA,UAAI,CAAC,KAAK,YAAY,IAAI,GAAG;AAC3B,cAAM,MAAM,4CAA4C,IAAI;AAAA,MAC9D;AAEA,UAAI,OAAO,UAAU,UAAU;AAC7B,cAAM,MAAM,yCAAyC,KAAK;AAAA,MAC5D;AAEA,UAAI,WAAW,QAAQ,WAAW,QAAW;AAC3C,iBAAS;AAAA,MACX;AAEA,WAAK,UAAU,EAAC,WAAW,SAAS,aAAa,OAAM,CAAC;AAExD,eAAS,KAAK,UAAU,OAAO,EAAC,UAAU,MAAM,gBAAgB,KAAI,CAAC;AAErE,WAAK,UAAU,UAAU,CAAC,CAAC;AAI3B,UAAI,OAAO,aAAa;AACtB,cAAM,OAAO;AAAA,MACf;AAEA,aAAO;AAAA,QACL,WAAW,OAAO;AAAA,QAClB,UAAU,OAAO;AAAA,QACjB,OAAO,OAAO,QAAQ,SAAS;AAAA,MACjC;AAAA,IACF;AAEA,aAAS,cAAc,OAAO,SAAS;AACrC,UAAI,WAAW,WAAW,CAAC;AAC3B,UAAI,SAAS,SAAS,UAAU,KAAK,cAAc;AACnD,UAAI,SAAS,SAAS;AACtB,UAAI,SAAS,OAAO;AACpB,UAAI,QAAQ;AACZ,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,UAAI;AAEJ,UAAI,WAAW,QAAQ,WAAW,QAAW;AAC3C,iBAAS;AAAA,MACX;AAEA,UAAI,OAAO,UAAU,UAAU;AAC7B,cAAM,MAAM,yCAAyC,KAAK;AAAA,MAC5D;AAEA,mBAAa,EAAC,WAAW,GAAG,UAAU,MAAM,OAAO,CAAC,EAAC;AACrD,eAAS,EAAC,WAAW,GAAG,UAAU,MAAM,OAAO,CAAC,EAAC;AAEjD,aAAO,EAAE,QAAQ,QAAQ;AACvB,eAAO,OAAO,KAAK;AAEnB,YAAI,CAAC,KAAK,YAAY,IAAI,GAAG;AAC3B;AAAA,QACF;AAEA,kBAAU,UAAU,MAAM,OAAO,OAAO;AACxC,gBAAQ,WAAW;AAEnB,YAAI,QAAQ,YAAY,WAAW,WAAW;AAC5C,uBAAa;AAAA,QACf;AAEA,YAAI,QAAQ,YAAY,OAAO,WAAW;AACxC,uBAAa;AACb,mBAAS;AAAA,QACX;AAAA,MACF;AAEA,UAAI,WAAW,UAAU;AACvB,eAAO,aAAa;AAAA,MACtB;AAEA,aAAO;AAAA,IACT;AAGA,aAAS,iBAAiB,MAAM,QAAQ;AACtC,WAAK,iBAAiB,MAAM,MAAM;AAAA,IACpC;AAGA,aAAS,gBAAgB;AACvB,aAAO,KAAK,cAAc;AAAA,IAC5B;AAGA,aAAS,cAAc,MAAM,OAAO;AAClC,UAAI,MAAM;AACV,UAAI;AAEJ,UAAI,OAAO;AACT,cAAM,CAAC;AACP,YAAI,IAAI,IAAI;AAAA,MACd;AAEA,WAAK,OAAO,KAAK;AACf,aAAK,gBAAgB,IAAI,GAAG,GAAG,EAAC,cAAc,IAAG,CAAC;AAAA,MACpD;AAAA,IACF;AAEA,aAAS,QAAQ,SAAS;AACxB,WAAK,UAAU;AACf,WAAK,WAAW,EAAC,UAAU,CAAC,EAAC;AAC7B,WAAK,QAAQ,CAAC,KAAK,QAAQ;AAAA,IAC7B;AAEA,aAAS,WAAW,OAAO,MAAM;AAC/B,WAAK,SAAS,IAAI;AAClB,WAAK,QAAQ,KAAK;AAClB,WAAK,UAAU;AAAA,IACjB;AAEA,aAAS,eAAe,OAAO,MAAM;AACnC,UAAI,QAAQ,KAAK;AACjB,UAAI,UAAU,MAAM,MAAM,SAAS,CAAC;AACpC,UAAI,UAAU,MAAM,SAAS;AAC7B,UAAI,OAAO,OACP;AAAA,QACE,MAAM;AAAA,QACN,SAAS;AAAA,QACT,YAAY,EAAC,WAAW,CAAC,IAAI,EAAC;AAAA,QAC9B,UAAU;AAAA,MACZ,IACA;AAEJ,cAAQ,WAAW,QAAQ,SAAS,OAAO,IAAI;AAAA,IACjD;AAEA,aAAS,KAAK,OAAO;AACnB,UAAI,QAAQ,KAAK;AACjB,UAAI;AACJ,UAAI;AAEJ,UAAI,UAAU,GAAI;AAElB,gBAAU,MAAM,MAAM,SAAS,CAAC;AAChC,aAAO,QAAQ,SAAS,QAAQ,SAAS,SAAS,CAAC;AAEnD,UAAI,QAAQ,KAAK,SAAS,QAAQ;AAChC,aAAK,SAAS;AAAA,MAChB,OAAO;AACL,gBAAQ,SAAS,KAAK,EAAC,MAAM,QAAQ,MAAY,CAAC;AAAA,MACpD;AAAA,IACF;AAEA,aAAS,KAAK,MAAM;AAClB,UAAI,QAAQ,KAAK;AACjB,UAAI,YAAY,KAAK,QAAQ,cAAc;AAC3C,UAAI,UAAU,MAAM,MAAM,SAAS,CAAC;AACpC,UAAI,QAAQ;AAAA,QACV,MAAM;AAAA,QACN,SAAS;AAAA,QACT,YAAY,EAAC,WAAW,CAAC,SAAS,EAAC;AAAA,QACnC,UAAU,CAAC;AAAA,MACb;AAEA,cAAQ,SAAS,KAAK,KAAK;AAC3B,YAAM,KAAK,KAAK;AAAA,IAClB;AAEA,aAAS,QAAQ;AACf,WAAK,MAAM,IAAI;AAAA,IACjB;AAEA,aAAS,aAAa;AACpB,aAAO;AAAA,IACT;AAEA,aAAS,OAAO;AAAA,IAAC;AAAA;AAAA;", "names": ["className", "i", "node", "version", "highlight", "result", "require_core"]}