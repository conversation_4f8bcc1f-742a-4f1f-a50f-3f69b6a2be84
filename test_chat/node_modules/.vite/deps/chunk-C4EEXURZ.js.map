{"version": 3, "sources": ["../../highlight.js/lib/languages/nginx.js"], "sourcesContent": ["/*\nLanguage: Nginx config\nAuthor: <PERSON> <<EMAIL>>\nContributors: <PERSON> <<EMAIL>>\nCategory: common, config\nWebsite: https://www.nginx.com\n*/\n\nfunction nginx(hljs) {\n  const VAR = {\n    className: 'variable',\n    variants: [\n      {\n        begin: /\\$\\d+/\n      },\n      {\n        begin: /\\$\\{/,\n        end: /\\}/\n      },\n      {\n        begin: /[$@]/ + hljs.UNDERSCORE_IDENT_RE\n      }\n    ]\n  };\n  const DEFAULT = {\n    endsWithParent: true,\n    keywords: {\n      $pattern: '[a-z/_]+',\n      literal:\n        'on off yes no true false none blocked debug info notice warn error crit ' +\n        'select break last permanent redirect kqueue rtsig epoll poll /dev/poll'\n    },\n    relevance: 0,\n    illegal: '=>',\n    contains: [\n      hljs.HASH_COMMENT_MODE,\n      {\n        className: 'string',\n        contains: [\n          hljs.BACKSLASH_ESCAPE,\n          VAR\n        ],\n        variants: [\n          {\n            begin: /\"/,\n            end: /\"/\n          },\n          {\n            begin: /'/,\n            end: /'/\n          }\n        ]\n      },\n      // this swallows entire URLs to avoid detecting numbers within\n      {\n        begin: '([a-z]+):/',\n        end: '\\\\s',\n        endsWithParent: true,\n        excludeEnd: true,\n        contains: [ VAR ]\n      },\n      {\n        className: 'regexp',\n        contains: [\n          hljs.BACKSLASH_ESCAPE,\n          VAR\n        ],\n        variants: [\n          {\n            begin: \"\\\\s\\\\^\",\n            end: \"\\\\s|\\\\{|;\",\n            returnEnd: true\n          },\n          // regexp locations (~, ~*)\n          {\n            begin: \"~\\\\*?\\\\s+\",\n            end: \"\\\\s|\\\\{|;\",\n            returnEnd: true\n          },\n          // *.example.com\n          {\n            begin: \"\\\\*(\\\\.[a-z\\\\-]+)+\"\n          },\n          // sub.example.*\n          {\n            begin: \"([a-z\\\\-]+\\\\.)+\\\\*\"\n          }\n        ]\n      },\n      // IP\n      {\n        className: 'number',\n        begin: '\\\\b\\\\d{1,3}\\\\.\\\\d{1,3}\\\\.\\\\d{1,3}\\\\.\\\\d{1,3}(:\\\\d{1,5})?\\\\b'\n      },\n      // units\n      {\n        className: 'number',\n        begin: '\\\\b\\\\d+[kKmMgGdshdwy]*\\\\b',\n        relevance: 0\n      },\n      VAR\n    ]\n  };\n\n  return {\n    name: 'Nginx config',\n    aliases: [ 'nginxconf' ],\n    contains: [\n      hljs.HASH_COMMENT_MODE,\n      {\n        begin: hljs.UNDERSCORE_IDENT_RE + '\\\\s+\\\\{',\n        returnBegin: true,\n        end: /\\{/,\n        contains: [\n          {\n            className: 'section',\n            begin: hljs.UNDERSCORE_IDENT_RE\n          }\n        ],\n        relevance: 0\n      },\n      {\n        begin: hljs.UNDERSCORE_IDENT_RE + '\\\\s',\n        end: ';|\\\\{',\n        returnBegin: true,\n        contains: [\n          {\n            className: 'attribute',\n            begin: hljs.UNDERSCORE_IDENT_RE,\n            starts: DEFAULT\n          }\n        ],\n        relevance: 0\n      }\n    ],\n    illegal: '[^\\\\s\\\\}]'\n  };\n}\n\nmodule.exports = nginx;\n"], "mappings": ";;;;;AAAA;AAAA;AAQA,aAAS,MAAM,MAAM;AACnB,YAAM,MAAM;AAAA,QACV,WAAW;AAAA,QACX,UAAU;AAAA,UACR;AAAA,YACE,OAAO;AAAA,UACT;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,UACP;AAAA,UACA;AAAA,YACE,OAAO,SAAS,KAAK;AAAA,UACvB;AAAA,QACF;AAAA,MACF;AACA,YAAM,UAAU;AAAA,QACd,gBAAgB;AAAA,QAChB,UAAU;AAAA,UACR,UAAU;AAAA,UACV,SACE;AAAA,QAEJ;AAAA,QACA,WAAW;AAAA,QACX,SAAS;AAAA,QACT,UAAU;AAAA,UACR,KAAK;AAAA,UACL;AAAA,YACE,WAAW;AAAA,YACX,UAAU;AAAA,cACR,KAAK;AAAA,cACL;AAAA,YACF;AAAA,YACA,UAAU;AAAA,cACR;AAAA,gBACE,OAAO;AAAA,gBACP,KAAK;AAAA,cACP;AAAA,cACA;AAAA,gBACE,OAAO;AAAA,gBACP,KAAK;AAAA,cACP;AAAA,YACF;AAAA,UACF;AAAA;AAAA,UAEA;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,YACL,gBAAgB;AAAA,YAChB,YAAY;AAAA,YACZ,UAAU,CAAE,GAAI;AAAA,UAClB;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,UAAU;AAAA,cACR,KAAK;AAAA,cACL;AAAA,YACF;AAAA,YACA,UAAU;AAAA,cACR;AAAA,gBACE,OAAO;AAAA,gBACP,KAAK;AAAA,gBACL,WAAW;AAAA,cACb;AAAA;AAAA,cAEA;AAAA,gBACE,OAAO;AAAA,gBACP,KAAK;AAAA,gBACL,WAAW;AAAA,cACb;AAAA;AAAA,cAEA;AAAA,gBACE,OAAO;AAAA,cACT;AAAA;AAAA,cAEA;AAAA,gBACE,OAAO;AAAA,cACT;AAAA,YACF;AAAA,UACF;AAAA;AAAA,UAEA;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,UACT;AAAA;AAAA,UAEA;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,YACP,WAAW;AAAA,UACb;AAAA,UACA;AAAA,QACF;AAAA,MACF;AAEA,aAAO;AAAA,QACL,MAAM;AAAA,QACN,SAAS,CAAE,WAAY;AAAA,QACvB,UAAU;AAAA,UACR,KAAK;AAAA,UACL;AAAA,YACE,OAAO,KAAK,sBAAsB;AAAA,YAClC,aAAa;AAAA,YACb,KAAK;AAAA,YACL,UAAU;AAAA,cACR;AAAA,gBACE,WAAW;AAAA,gBACX,OAAO,KAAK;AAAA,cACd;AAAA,YACF;AAAA,YACA,WAAW;AAAA,UACb;AAAA,UACA;AAAA,YACE,OAAO,KAAK,sBAAsB;AAAA,YAClC,KAAK;AAAA,YACL,aAAa;AAAA,YACb,UAAU;AAAA,cACR;AAAA,gBACE,WAAW;AAAA,gBACX,OAAO,KAAK;AAAA,gBACZ,QAAQ;AAAA,cACV;AAAA,YACF;AAAA,YACA,WAAW;AAAA,UACb;AAAA,QACF;AAAA,QACA,SAAS;AAAA,MACX;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}