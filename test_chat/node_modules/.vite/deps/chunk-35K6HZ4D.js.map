{"version": 3, "sources": ["../../highlight.js/lib/languages/gcode.js"], "sourcesContent": ["/*\n Language: G-code (ISO 6983)\n Contributors: <PERSON> <<EMAIL>>\n Description: G-code syntax highlighter for Fanuc and other common CNC machine tool controls.\n Website: https://www.sis.se/api/document/preview/911952/\n */\n\nfunction gcode(hljs) {\n  const GCODE_IDENT_RE = '[A-Z_][A-Z0-9_.]*';\n  const GCODE_CLOSE_RE = '%';\n  const GCODE_KEYWORDS = {\n    $pattern: GCODE_IDENT_RE,\n    keyword: 'IF DO WHILE ENDWHILE CALL ENDIF SUB ENDSUB GOTO REPEAT ENDREPEAT ' +\n      'EQ LT GT NE GE LE OR XOR'\n  };\n  const GCODE_START = {\n    className: 'meta',\n    begin: '([O])([0-9]+)'\n  };\n  const NUMBER = hljs.inherit(hljs.C_NUMBER_MODE, {\n    begin: '([-+]?((\\\\.\\\\d+)|(\\\\d+)(\\\\.\\\\d*)?))|' + hljs.C_NUMBER_RE\n  });\n  const GCODE_CODE = [\n    hljs.C_LINE_COMMENT_MODE,\n    hljs.C_BLOCK_COMMENT_MODE,\n    hljs.COMMENT(/\\(/, /\\)/),\n    NUMBER,\n    hljs.inherit(hljs.APOS_STRING_MODE, {\n      illegal: null\n    }),\n    hljs.inherit(hljs.QUOTE_STRING_MODE, {\n      illegal: null\n    }),\n    {\n      className: 'name',\n      begin: '([G])([0-9]+\\\\.?[0-9]?)'\n    },\n    {\n      className: 'name',\n      begin: '([M])([0-9]+\\\\.?[0-9]?)'\n    },\n    {\n      className: 'attr',\n      begin: '(VC|VS|#)',\n      end: '(\\\\d+)'\n    },\n    {\n      className: 'attr',\n      begin: '(VZOFX|VZOFY|VZOFZ)'\n    },\n    {\n      className: 'built_in',\n      begin: '(ATAN|ABS|ACOS|ASIN|SIN|COS|EXP|FIX|FUP|ROUND|LN|TAN)(\\\\[)',\n      contains: [\n        NUMBER\n      ],\n      end: '\\\\]'\n    },\n    {\n      className: 'symbol',\n      variants: [\n        {\n          begin: 'N',\n          end: '\\\\d+',\n          illegal: '\\\\W'\n        }\n      ]\n    }\n  ];\n\n  return {\n    name: 'G-code (ISO 6983)',\n    aliases: ['nc'],\n    // Some implementations (CNC controls) of G-code are interoperable with uppercase and lowercase letters seamlessly.\n    // However, most prefer all uppercase and uppercase is customary.\n    case_insensitive: true,\n    keywords: GCODE_KEYWORDS,\n    contains: [\n      {\n        className: 'meta',\n        begin: GCODE_CLOSE_RE\n      },\n      GCODE_START\n    ].concat(GCODE_CODE)\n  };\n}\n\nmodule.exports = gcode;\n"], "mappings": ";;;;;AAAA;AAAA;AAOA,aAAS,MAAM,MAAM;AACnB,YAAM,iBAAiB;AACvB,YAAM,iBAAiB;AACvB,YAAM,iBAAiB;AAAA,QACrB,UAAU;AAAA,QACV,SAAS;AAAA,MAEX;AACA,YAAM,cAAc;AAAA,QAClB,WAAW;AAAA,QACX,OAAO;AAAA,MACT;AACA,YAAM,SAAS,KAAK,QAAQ,KAAK,eAAe;AAAA,QAC9C,OAAO,yCAAyC,KAAK;AAAA,MACvD,CAAC;AACD,YAAM,aAAa;AAAA,QACjB,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK,QAAQ,MAAM,IAAI;AAAA,QACvB;AAAA,QACA,KAAK,QAAQ,KAAK,kBAAkB;AAAA,UAClC,SAAS;AAAA,QACX,CAAC;AAAA,QACD,KAAK,QAAQ,KAAK,mBAAmB;AAAA,UACnC,SAAS;AAAA,QACX,CAAC;AAAA,QACD;AAAA,UACE,WAAW;AAAA,UACX,OAAO;AAAA,QACT;AAAA,QACA;AAAA,UACE,WAAW;AAAA,UACX,OAAO;AAAA,QACT;AAAA,QACA;AAAA,UACE,WAAW;AAAA,UACX,OAAO;AAAA,UACP,KAAK;AAAA,QACP;AAAA,QACA;AAAA,UACE,WAAW;AAAA,UACX,OAAO;AAAA,QACT;AAAA,QACA;AAAA,UACE,WAAW;AAAA,UACX,OAAO;AAAA,UACP,UAAU;AAAA,YACR;AAAA,UACF;AAAA,UACA,KAAK;AAAA,QACP;AAAA,QACA;AAAA,UACE,WAAW;AAAA,UACX,UAAU;AAAA,YACR;AAAA,cACE,OAAO;AAAA,cACP,KAAK;AAAA,cACL,SAAS;AAAA,YACX;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAEA,aAAO;AAAA,QACL,MAAM;AAAA,QACN,SAAS,CAAC,IAAI;AAAA;AAAA;AAAA,QAGd,kBAAkB;AAAA,QAClB,UAAU;AAAA,QACV,UAAU;AAAA,UACR;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,UACT;AAAA,UACA;AAAA,QACF,EAAE,OAAO,UAAU;AAAA,MACrB;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}