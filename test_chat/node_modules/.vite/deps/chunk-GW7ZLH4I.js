import {
  require_xojo
} from "./chunk-H5LBIMSD.js";
import {
  require_xquery
} from "./chunk-RXQNCEQM.js";
import {
  require_yang
} from "./chunk-ETT4CMN3.js";
import {
  require_zig
} from "./chunk-NYVY23WB.js";
import {
  require_core
} from "./chunk-OYTLD4UU.js";
import {
  require_warpscript
} from "./chunk-N327FID7.js";
import {
  require_wasm
} from "./chunk-UQ2E6EY3.js";
import {
  require_web_idl
} from "./chunk-Q742DFIA.js";
import {
  require_wiki
} from "./chunk-L4GHOZCI.js";
import {
  require_wolfram
} from "./chunk-F6S3XU4Q.js";
import {
  require_wren
} from "./chunk-ISJNLGRJ.js";
import {
  require_xeora
} from "./chunk-PSIUHI32.js";
import {
  require_xml_doc
} from "./chunk-7JK7ZRL5.js";
import {
  require_uri
} from "./chunk-5NLMFLCF.js";
import {
  require_v
} from "./chunk-JAGV2TJD.js";
import {
  require_vala
} from "./chunk-ZVPRMJV2.js";
import {
  require_velocity
} from "./chunk-DJ7ATH4N.js";
import {
  require_verilog
} from "./chunk-H62SYTQU.js";
import {
  require_vhdl
} from "./chunk-LMAFSY2R.js";
import {
  require_vim
} from "./chunk-IJPXWVH3.js";
import {
  require_visual_basic
} from "./chunk-45QVFZOB.js";
import {
  require_toml
} from "./chunk-JXKUI4B7.js";
import {
  require_tremor
} from "./chunk-WTXLYQF4.js";
import {
  require_tsx
} from "./chunk-K352ERNO.js";
import {
  require_tt2
} from "./chunk-FMDV5RCK.js";
import {
  require_twig
} from "./chunk-7VMANK5L.js";
import {
  require_typoscript
} from "./chunk-URUJOSUJ.js";
import {
  require_unrealscript
} from "./chunk-RBVYGCVD.js";
import {
  require_uorazor
} from "./chunk-M3KPONMB.js";
import {
  require_t4_cs
} from "./chunk-ALDDERU2.js";
import {
  require_t4_vb
} from "./chunk-RH7JINPN.js";
import {
  require_t4_templating
} from "./chunk-TTCB42LP.js";
import {
  require_vbnet
} from "./chunk-L5VFTYPX.js";
import {
  require_tap
} from "./chunk-JF4PEHHI.js";
import {
  require_yaml
} from "./chunk-IA7T2MXR.js";
import {
  require_tcl
} from "./chunk-VZYT77PZ.js";
import {
  require_textile
} from "./chunk-E2KY547C.js";
import {
  require_sparql
} from "./chunk-4KWXGKFP.js";
import {
  require_splunk_spl
} from "./chunk-UURJD6NS.js";
import {
  require_sqf
} from "./chunk-BJJPXBS3.js";
import {
  require_squirrel
} from "./chunk-5IV5VSTI.js";
import {
  require_stan
} from "./chunk-YMZPUAJG.js";
import {
  require_stylus
} from "./chunk-URACKOSR.js";
import {
  require_swift
} from "./chunk-QYFUKD7D.js";
import {
  require_systemd
} from "./chunk-AXN5CREW.js";
import {
  require_smali
} from "./chunk-QPW2WK6A.js";
import {
  require_smalltalk
} from "./chunk-5XIHPGFN.js";
import {
  require_smarty
} from "./chunk-PAZ2QJRG.js";
import {
  require_sml
} from "./chunk-2WLIC23G.js";
import {
  require_solidity
} from "./chunk-WKNQGVOA.js";
import {
  require_solution_file
} from "./chunk-3G4GP7YO.js";
import {
  require_soy
} from "./chunk-XOKGFMVE.js";
import {
  require_turtle
} from "./chunk-QGGOPY2B.js";
import {
  require_roboconf
} from "./chunk-745FJQNS.js";
import {
  require_robotframework
} from "./chunk-JTHKXIFG.js";
import {
  require_rust
} from "./chunk-GNR4RKRZ.js";
import {
  require_sas
} from "./chunk-4XYKQC4B.js";
import {
  require_sass
} from "./chunk-GO54NCRR.js";
import {
  require_scala
} from "./chunk-27X7CGDF.js";
import {
  require_scss
} from "./chunk-5VXVS7AR.js";
import {
  require_shell_session
} from "./chunk-ZC26MDXA.js";
import {
  require_r
} from "./chunk-7GVYX4OO.js";
import {
  require_racket
} from "./chunk-FNA5OTXB.js";
import {
  require_reason
} from "./chunk-PHYPWFDC.js";
import {
  require_regex
} from "./chunk-3AVRGSRD.js";
import {
  require_rego
} from "./chunk-IBFVDTXA.js";
import {
  require_renpy
} from "./chunk-5IWKE7HL.js";
import {
  require_rest
} from "./chunk-L5UAMXRI.js";
import {
  require_rip
} from "./chunk-QPLGVDOZ.js";
import {
  require_pure
} from "./chunk-ZZP6QNJN.js";
import {
  require_purebasic
} from "./chunk-DMHDSZYL.js";
import {
  require_purescript
} from "./chunk-ULOI4K2V.js";
import {
  require_python
} from "./chunk-3TIWLMIM.js";
import {
  require_q
} from "./chunk-MVQLPJ7I.js";
import {
  require_qml
} from "./chunk-OMXDSNE3.js";
import {
  require_qore
} from "./chunk-AUGJGPAT.js";
import {
  require_qsharp
} from "./chunk-UFDSBAMS.js";
import {
  require_processing
} from "./chunk-RF743XW4.js";
import {
  require_prolog
} from "./chunk-PMJSNCLS.js";
import {
  require_promql
} from "./chunk-PUT4HYWR.js";
import {
  require_properties
} from "./chunk-CYS4FYUN.js";
import {
  require_protobuf
} from "./chunk-V6UFGXAH.js";
import {
  require_psl
} from "./chunk-255DYT6C.js";
import {
  require_pug
} from "./chunk-AIH4VHUY.js";
import {
  require_puppet
} from "./chunk-J7RPM3DH.js";
import {
  require_pcaxis
} from "./chunk-5LJBMLVS.js";
import {
  require_peoplecode
} from "./chunk-QOQRLVRR.js";
import {
  require_perl
} from "./chunk-6DH244VY.js";
import {
  require_php_extras
} from "./chunk-6D5DW2FW.js";
import {
  require_phpdoc
} from "./chunk-S276KH6Q.js";
import {
  require_plsql
} from "./chunk-YG2GDG2Z.js";
import {
  require_powerquery
} from "./chunk-CGAWJQY7.js";
import {
  require_powershell
} from "./chunk-GPZEY5WB.js";
import {
  require_ocaml
} from "./chunk-32MM5ZX4.js";
import {
  require_opencl
} from "./chunk-7LCD4BP4.js";
import {
  require_openqasm
} from "./chunk-SLQ7PGLD.js";
import {
  require_oz
} from "./chunk-DLIXEZJS.js";
import {
  require_parigp
} from "./chunk-NVK4QYID.js";
import {
  require_parser
} from "./chunk-2XSKG55U.js";
import {
  require_pascal
} from "./chunk-T34TJ6IT.js";
import {
  require_pascaligo
} from "./chunk-H7B4CTBK.js";
import {
  require_nasm
} from "./chunk-LVLKDONZ.js";
import {
  require_neon
} from "./chunk-XTG6M6R6.js";
import {
  require_nevod
} from "./chunk-BGQ54XF5.js";
import {
  require_nginx
} from "./chunk-5L4VHEFN.js";
import {
  require_nim
} from "./chunk-5KFIYGDA.js";
import {
  require_nix
} from "./chunk-BRQUGG4O.js";
import {
  require_nsis
} from "./chunk-UV3TBD27.js";
import {
  require_objectivec
} from "./chunk-QDZRGMZX.js";
import {
  require_mizar
} from "./chunk-L6KZIFJV.js";
import {
  require_mongodb
} from "./chunk-F423HY2E.js";
import {
  require_monkey
} from "./chunk-H7EUDSG3.js";
import {
  require_moonscript
} from "./chunk-QXY27HF2.js";
import {
  require_n1ql
} from "./chunk-OIP4M7WN.js";
import {
  require_n4js
} from "./chunk-7FXP27JW.js";
import {
  require_nand2tetris_hdl
} from "./chunk-XIKTR56D.js";
import {
  require_naniscript
} from "./chunk-JKHA3JVS.js";
import {
  require_magma
} from "./chunk-JKOVUWNZ.js";
import {
  require_makefile
} from "./chunk-CSO6OS4O.js";
import {
  require_markdown
} from "./chunk-EZU75OMO.js";
import {
  require_matlab
} from "./chunk-GI72WR77.js";
import {
  require_maxscript
} from "./chunk-PAV2RHSN.js";
import {
  require_mel
} from "./chunk-EERZ2PT3.js";
import {
  require_mermaid
} from "./chunk-6WFL773Z.js";
import {
  require_lilypond
} from "./chunk-KWPWPFL2.js";
import {
  require_scheme
} from "./chunk-NHJBNPII.js";
import {
  require_liquid
} from "./chunk-GJGDQPF7.js";
import {
  require_lisp
} from "./chunk-B3CIRX7U.js";
import {
  require_livescript
} from "./chunk-4KWPWMXH.js";
import {
  require_llvm
} from "./chunk-4KWEC4LS.js";
import {
  require_log
} from "./chunk-M3MYNRY2.js";
import {
  require_lolcode
} from "./chunk-4LVOO47C.js";
import {
  require_keyman
} from "./chunk-G6RNX5PA.js";
import {
  require_kotlin
} from "./chunk-F7AYLA72.js";
import {
  require_kumir
} from "./chunk-C7OV2ZWZ.js";
import {
  require_kusto
} from "./chunk-WHS3ZVQ7.js";
import {
  require_latex
} from "./chunk-23AC4FZO.js";
import {
  require_latte
} from "./chunk-NKME456S.js";
import {
  require_php
} from "./chunk-GVSIUJSX.js";
import {
  require_less
} from "./chunk-Q323JI4Z.js";
import {
  require_jsdoc
} from "./chunk-NXHWN3AU.js";
import {
  require_json5
} from "./chunk-QSUA327X.js";
import {
  require_jsonp
} from "./chunk-6MQV7GXH.js";
import {
  require_json
} from "./chunk-376XAWBK.js";
import {
  require_jsstacktrace
} from "./chunk-DTHN3HYC.js";
import {
  require_jsx
} from "./chunk-IBT7FFNF.js";
import {
  require_julia
} from "./chunk-4KDC3GRE.js";
import {
  require_keepalived
} from "./chunk-YT5LVHR2.js";
import {
  require_javastacktrace
} from "./chunk-BDYCJY4O.js";
import {
  require_jexl
} from "./chunk-5XLF2V3M.js";
import {
  require_jolie
} from "./chunk-VO5ZPTYY.js";
import {
  require_jq
} from "./chunk-D7IMIYKZ.js";
import {
  require_js_extras
} from "./chunk-JH7AFGHP.js";
import {
  require_js_templates
} from "./chunk-KJVKMJVY.js";
import {
  require_typescript
} from "./chunk-D532R36F.js";
import {
  require_ignore
} from "./chunk-7AJ4E6JZ.js";
import {
  require_inform7
} from "./chunk-6AFQDJQW.js";
import {
  require_ini
} from "./chunk-A5JBH6TK.js";
import {
  require_io
} from "./chunk-B5OI43CQ.js";
import {
  require_j
} from "./chunk-HVGMMH4U.js";
import {
  require_javadoc
} from "./chunk-SHLWMB6K.js";
import {
  require_java
} from "./chunk-YSHDUHNT.js";
import {
  require_javadoclike
} from "./chunk-FPFWYE3Z.js";
import {
  require_hpkp
} from "./chunk-YWRON2GZ.js";
import {
  require_hsts
} from "./chunk-OBUJPQ3E.js";
import {
  require_http
} from "./chunk-HF6KWTNT.js";
import {
  require_ichigojam
} from "./chunk-7SR3URHT.js";
import {
  require_icon
} from "./chunk-RL6WF6YS.js";
import {
  require_icu_message_format
} from "./chunk-EHKALW3P.js";
import {
  require_idris
} from "./chunk-WFBKFZE6.js";
import {
  require_iecst
} from "./chunk-5M7NSRNH.js";
import {
  require_groovy
} from "./chunk-VQM6ATLP.js";
import {
  require_haml
} from "./chunk-43K4JMIQ.js";
import {
  require_handlebars
} from "./chunk-HJRQV3HW.js";
import {
  require_haskell
} from "./chunk-EI5FZLGD.js";
import {
  require_haxe
} from "./chunk-3RHSRLNX.js";
import {
  require_hcl
} from "./chunk-INV3MNKO.js";
import {
  require_hlsl
} from "./chunk-GUZIO3N5.js";
import {
  require_hoon
} from "./chunk-HUN4DCRY.js";
import {
  require_gherkin
} from "./chunk-YCMRBZQA.js";
import {
  require_git
} from "./chunk-PNSJ3D2M.js";
import {
  require_glsl
} from "./chunk-LOYJP7FG.js";
import {
  require_gml
} from "./chunk-7AXOADLE.js";
import {
  require_gn
} from "./chunk-2O4TYYM5.js";
import {
  require_go_module
} from "./chunk-BSXWLQER.js";
import {
  require_go
} from "./chunk-XWJ7Z47A.js";
import {
  require_graphql
} from "./chunk-CNSIRLZT.js";
import {
  require_flow
} from "./chunk-MU3GZRS5.js";
import {
  require_fortran
} from "./chunk-CJCKXIOD.js";
import {
  require_fsharp
} from "./chunk-JI7Y4XND.js";
import {
  require_ftl
} from "./chunk-UW7R5ZU6.js";
import {
  require_gap
} from "./chunk-E5QKWPJG.js";
import {
  require_gcode
} from "./chunk-MWAD63XE.js";
import {
  require_gdscript
} from "./chunk-7TM37DPX.js";
import {
  require_gedcom
} from "./chunk-NQVVJI7J.js";
import {
  require_erb
} from "./chunk-YJVQZKKP.js";
import {
  require_erlang
} from "./chunk-XZI77OLY.js";
import {
  require_etlua
} from "./chunk-KCI6OK3S.js";
import {
  require_lua
} from "./chunk-N3XWDO2D.js";
import {
  require_excel_formula
} from "./chunk-NPH7QPSU.js";
import {
  require_factor
} from "./chunk-U7RXEW4Y.js";
import {
  require_false
} from "./chunk-VWZOMI2Q.js";
import {
  require_firestore_security_rules
} from "./chunk-ODF6R76H.js";
import {
  require_docker
} from "./chunk-DX6HXGS2.js";
import {
  require_dot
} from "./chunk-UC6UK4LK.js";
import {
  require_ebnf
} from "./chunk-Q43XPOZ5.js";
import {
  require_editorconfig
} from "./chunk-2EDMOEFB.js";
import {
  require_eiffel
} from "./chunk-T64VCLBP.js";
import {
  require_ejs
} from "./chunk-553QSIPC.js";
import {
  require_elixir
} from "./chunk-3NNIH3JE.js";
import {
  require_elm
} from "./chunk-OPBEWIXO.js";
import {
  require_dart
} from "./chunk-SCBBK3IN.js";
import {
  require_dataweave
} from "./chunk-PWAS444Q.js";
import {
  require_dax
} from "./chunk-Z2MIROZJ.js";
import {
  require_dhall
} from "./chunk-Y4LVORRF.js";
import {
  require_diff
} from "./chunk-WY26ZMJF.js";
import {
  require_django
} from "./chunk-ICOTX67I.js";
import {
  require_markup_templating
} from "./chunk-T3IPAXQI.js";
import {
  require_dns_zone_file
} from "./chunk-PZLAVEGZ.js";
import {
  require_crystal
} from "./chunk-XGKJZXKB.js";
import {
  require_cshtml
} from "./chunk-2S4X26MU.js";
import {
  require_csp
} from "./chunk-3H2AAJ46.js";
import {
  require_css_extras
} from "./chunk-CE7HWCP3.js";
import {
  require_csv
} from "./chunk-VOH3JTDD.js";
import {
  require_cypher
} from "./chunk-Q4EBSTYJ.js";
import {
  require_d
} from "./chunk-QCEJB3F2.js";
import {
  require_clojure
} from "./chunk-3WR552XK.js";
import {
  require_cmake
} from "./chunk-INRORV4J.js";
import {
  require_cobol
} from "./chunk-ROCK2QBB.js";
import {
  require_coffeescript
} from "./chunk-QJNIVSAK.js";
import {
  require_concurnas
} from "./chunk-74JIZER2.js";
import {
  require_coq
} from "./chunk-OA4Y3SPH.js";
import {
  require_ruby
} from "./chunk-CQ2J2ZOZ.js";
import {
  require_bnf
} from "./chunk-MR7DJFF7.js";
import {
  require_brainfuck
} from "./chunk-UJ2KEEO6.js";
import {
  require_brightscript
} from "./chunk-DQSX3ZYV.js";
import {
  require_bro
} from "./chunk-ECSBSNKY.js";
import {
  require_bsl
} from "./chunk-SQLTRR2F.js";
import {
  require_cfscript
} from "./chunk-MF73BJTW.js";
import {
  require_chaiscript
} from "./chunk-FMTXOJXR.js";
import {
  require_cil
} from "./chunk-ISRJB4JU.js";
import {
  require_avro_idl
} from "./chunk-KN3SQONT.js";
import {
  require_bash
} from "./chunk-NT475Y4I.js";
import {
  require_basic
} from "./chunk-GZRQMMCR.js";
import {
  require_batch
} from "./chunk-7WVDJSJT.js";
import {
  require_bbcode
} from "./chunk-25JHHCOX.js";
import {
  require_bicep
} from "./chunk-6WUSYZTS.js";
import {
  require_birb
} from "./chunk-NQQJK7SP.js";
import {
  require_bison
} from "./chunk-P6AR7BTZ.js";
import {
  require_asciidoc
} from "./chunk-66ZBYU3F.js";
import {
  require_asm6502
} from "./chunk-GECZHEWB.js";
import {
  require_asmatmel
} from "./chunk-X357WTVP.js";
import {
  require_aspnet
} from "./chunk-IHY6IM42.js";
import {
  require_csharp
} from "./chunk-UEW6EHPX.js";
import {
  require_autohotkey
} from "./chunk-IZZTLFSO.js";
import {
  require_autoit
} from "./chunk-3PI5U6CZ.js";
import {
  require_avisynth
} from "./chunk-PPKKEEZS.js";
import {
  require_apex
} from "./chunk-YSGK26DE.js";
import {
  require_apl
} from "./chunk-NMFUSJTG.js";
import {
  require_applescript
} from "./chunk-C7G7WSEK.js";
import {
  require_aql
} from "./chunk-ED3XOURR.js";
import {
  require_arduino
} from "./chunk-TYTYEHAA.js";
import {
  require_cpp
} from "./chunk-XEQ2CVBM.js";
import {
  require_c
} from "./chunk-IC4DWTYF.js";
import {
  require_arff
} from "./chunk-Y3B64RBX.js";
import {
  require_abnf
} from "./chunk-5FVQWTRX.js";
import {
  require_actionscript
} from "./chunk-VBHCZX4D.js";
import {
  require_ada
} from "./chunk-4RLZHEOU.js";
import {
  require_agda
} from "./chunk-7GRVAFIR.js";
import {
  require_al
} from "./chunk-IMBI6PNJ.js";
import {
  require_antlr4
} from "./chunk-WJPSB32J.js";
import {
  require_apacheconf
} from "./chunk-VGVKRNL7.js";
import {
  require_sql
} from "./chunk-J73RUTBI.js";
import {
  require_abap
} from "./chunk-2NZSJKR7.js";
import {
  __commonJS
} from "./chunk-PLDDJCW6.js";

// node_modules/refractor/index.js
var require_refractor = __commonJS({
  "node_modules/refractor/index.js"(exports, module) {
    var refractor = require_core();
    module.exports = refractor;
    refractor.register(require_abap());
    refractor.register(require_abnf());
    refractor.register(require_actionscript());
    refractor.register(require_ada());
    refractor.register(require_agda());
    refractor.register(require_al());
    refractor.register(require_antlr4());
    refractor.register(require_apacheconf());
    refractor.register(require_apex());
    refractor.register(require_apl());
    refractor.register(require_applescript());
    refractor.register(require_aql());
    refractor.register(require_arduino());
    refractor.register(require_arff());
    refractor.register(require_asciidoc());
    refractor.register(require_asm6502());
    refractor.register(require_asmatmel());
    refractor.register(require_aspnet());
    refractor.register(require_autohotkey());
    refractor.register(require_autoit());
    refractor.register(require_avisynth());
    refractor.register(require_avro_idl());
    refractor.register(require_bash());
    refractor.register(require_basic());
    refractor.register(require_batch());
    refractor.register(require_bbcode());
    refractor.register(require_bicep());
    refractor.register(require_birb());
    refractor.register(require_bison());
    refractor.register(require_bnf());
    refractor.register(require_brainfuck());
    refractor.register(require_brightscript());
    refractor.register(require_bro());
    refractor.register(require_bsl());
    refractor.register(require_c());
    refractor.register(require_cfscript());
    refractor.register(require_chaiscript());
    refractor.register(require_cil());
    refractor.register(require_clojure());
    refractor.register(require_cmake());
    refractor.register(require_cobol());
    refractor.register(require_coffeescript());
    refractor.register(require_concurnas());
    refractor.register(require_coq());
    refractor.register(require_cpp());
    refractor.register(require_crystal());
    refractor.register(require_csharp());
    refractor.register(require_cshtml());
    refractor.register(require_csp());
    refractor.register(require_css_extras());
    refractor.register(require_csv());
    refractor.register(require_cypher());
    refractor.register(require_d());
    refractor.register(require_dart());
    refractor.register(require_dataweave());
    refractor.register(require_dax());
    refractor.register(require_dhall());
    refractor.register(require_diff());
    refractor.register(require_django());
    refractor.register(require_dns_zone_file());
    refractor.register(require_docker());
    refractor.register(require_dot());
    refractor.register(require_ebnf());
    refractor.register(require_editorconfig());
    refractor.register(require_eiffel());
    refractor.register(require_ejs());
    refractor.register(require_elixir());
    refractor.register(require_elm());
    refractor.register(require_erb());
    refractor.register(require_erlang());
    refractor.register(require_etlua());
    refractor.register(require_excel_formula());
    refractor.register(require_factor());
    refractor.register(require_false());
    refractor.register(require_firestore_security_rules());
    refractor.register(require_flow());
    refractor.register(require_fortran());
    refractor.register(require_fsharp());
    refractor.register(require_ftl());
    refractor.register(require_gap());
    refractor.register(require_gcode());
    refractor.register(require_gdscript());
    refractor.register(require_gedcom());
    refractor.register(require_gherkin());
    refractor.register(require_git());
    refractor.register(require_glsl());
    refractor.register(require_gml());
    refractor.register(require_gn());
    refractor.register(require_go_module());
    refractor.register(require_go());
    refractor.register(require_graphql());
    refractor.register(require_groovy());
    refractor.register(require_haml());
    refractor.register(require_handlebars());
    refractor.register(require_haskell());
    refractor.register(require_haxe());
    refractor.register(require_hcl());
    refractor.register(require_hlsl());
    refractor.register(require_hoon());
    refractor.register(require_hpkp());
    refractor.register(require_hsts());
    refractor.register(require_http());
    refractor.register(require_ichigojam());
    refractor.register(require_icon());
    refractor.register(require_icu_message_format());
    refractor.register(require_idris());
    refractor.register(require_iecst());
    refractor.register(require_ignore());
    refractor.register(require_inform7());
    refractor.register(require_ini());
    refractor.register(require_io());
    refractor.register(require_j());
    refractor.register(require_java());
    refractor.register(require_javadoc());
    refractor.register(require_javadoclike());
    refractor.register(require_javastacktrace());
    refractor.register(require_jexl());
    refractor.register(require_jolie());
    refractor.register(require_jq());
    refractor.register(require_js_extras());
    refractor.register(require_js_templates());
    refractor.register(require_jsdoc());
    refractor.register(require_json());
    refractor.register(require_json5());
    refractor.register(require_jsonp());
    refractor.register(require_jsstacktrace());
    refractor.register(require_jsx());
    refractor.register(require_julia());
    refractor.register(require_keepalived());
    refractor.register(require_keyman());
    refractor.register(require_kotlin());
    refractor.register(require_kumir());
    refractor.register(require_kusto());
    refractor.register(require_latex());
    refractor.register(require_latte());
    refractor.register(require_less());
    refractor.register(require_lilypond());
    refractor.register(require_liquid());
    refractor.register(require_lisp());
    refractor.register(require_livescript());
    refractor.register(require_llvm());
    refractor.register(require_log());
    refractor.register(require_lolcode());
    refractor.register(require_lua());
    refractor.register(require_magma());
    refractor.register(require_makefile());
    refractor.register(require_markdown());
    refractor.register(require_markup_templating());
    refractor.register(require_matlab());
    refractor.register(require_maxscript());
    refractor.register(require_mel());
    refractor.register(require_mermaid());
    refractor.register(require_mizar());
    refractor.register(require_mongodb());
    refractor.register(require_monkey());
    refractor.register(require_moonscript());
    refractor.register(require_n1ql());
    refractor.register(require_n4js());
    refractor.register(require_nand2tetris_hdl());
    refractor.register(require_naniscript());
    refractor.register(require_nasm());
    refractor.register(require_neon());
    refractor.register(require_nevod());
    refractor.register(require_nginx());
    refractor.register(require_nim());
    refractor.register(require_nix());
    refractor.register(require_nsis());
    refractor.register(require_objectivec());
    refractor.register(require_ocaml());
    refractor.register(require_opencl());
    refractor.register(require_openqasm());
    refractor.register(require_oz());
    refractor.register(require_parigp());
    refractor.register(require_parser());
    refractor.register(require_pascal());
    refractor.register(require_pascaligo());
    refractor.register(require_pcaxis());
    refractor.register(require_peoplecode());
    refractor.register(require_perl());
    refractor.register(require_php_extras());
    refractor.register(require_php());
    refractor.register(require_phpdoc());
    refractor.register(require_plsql());
    refractor.register(require_powerquery());
    refractor.register(require_powershell());
    refractor.register(require_processing());
    refractor.register(require_prolog());
    refractor.register(require_promql());
    refractor.register(require_properties());
    refractor.register(require_protobuf());
    refractor.register(require_psl());
    refractor.register(require_pug());
    refractor.register(require_puppet());
    refractor.register(require_pure());
    refractor.register(require_purebasic());
    refractor.register(require_purescript());
    refractor.register(require_python());
    refractor.register(require_q());
    refractor.register(require_qml());
    refractor.register(require_qore());
    refractor.register(require_qsharp());
    refractor.register(require_r());
    refractor.register(require_racket());
    refractor.register(require_reason());
    refractor.register(require_regex());
    refractor.register(require_rego());
    refractor.register(require_renpy());
    refractor.register(require_rest());
    refractor.register(require_rip());
    refractor.register(require_roboconf());
    refractor.register(require_robotframework());
    refractor.register(require_ruby());
    refractor.register(require_rust());
    refractor.register(require_sas());
    refractor.register(require_sass());
    refractor.register(require_scala());
    refractor.register(require_scheme());
    refractor.register(require_scss());
    refractor.register(require_shell_session());
    refractor.register(require_smali());
    refractor.register(require_smalltalk());
    refractor.register(require_smarty());
    refractor.register(require_sml());
    refractor.register(require_solidity());
    refractor.register(require_solution_file());
    refractor.register(require_soy());
    refractor.register(require_sparql());
    refractor.register(require_splunk_spl());
    refractor.register(require_sqf());
    refractor.register(require_sql());
    refractor.register(require_squirrel());
    refractor.register(require_stan());
    refractor.register(require_stylus());
    refractor.register(require_swift());
    refractor.register(require_systemd());
    refractor.register(require_t4_cs());
    refractor.register(require_t4_templating());
    refractor.register(require_t4_vb());
    refractor.register(require_tap());
    refractor.register(require_tcl());
    refractor.register(require_textile());
    refractor.register(require_toml());
    refractor.register(require_tremor());
    refractor.register(require_tsx());
    refractor.register(require_tt2());
    refractor.register(require_turtle());
    refractor.register(require_twig());
    refractor.register(require_typescript());
    refractor.register(require_typoscript());
    refractor.register(require_unrealscript());
    refractor.register(require_uorazor());
    refractor.register(require_uri());
    refractor.register(require_v());
    refractor.register(require_vala());
    refractor.register(require_vbnet());
    refractor.register(require_velocity());
    refractor.register(require_verilog());
    refractor.register(require_vhdl());
    refractor.register(require_vim());
    refractor.register(require_visual_basic());
    refractor.register(require_warpscript());
    refractor.register(require_wasm());
    refractor.register(require_web_idl());
    refractor.register(require_wiki());
    refractor.register(require_wolfram());
    refractor.register(require_wren());
    refractor.register(require_xeora());
    refractor.register(require_xml_doc());
    refractor.register(require_xojo());
    refractor.register(require_xquery());
    refractor.register(require_yaml());
    refractor.register(require_yang());
    refractor.register(require_zig());
  }
});

export {
  require_refractor
};
//# sourceMappingURL=chunk-GW7ZLH4I.js.map
