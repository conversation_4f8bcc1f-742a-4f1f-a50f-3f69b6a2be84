{"version": 3, "sources": ["../../refractor/lang/elixir.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = elixir\nelixir.displayName = 'elixir'\nelixir.aliases = []\nfunction elixir(Prism) {\n  Prism.languages.elixir = {\n    doc: {\n      pattern:\n        /@(?:doc|moduledoc)\\s+(?:(\"\"\"|''')[\\s\\S]*?\\1|(\"|')(?:\\\\(?:\\r\\n|[\\s\\S])|(?!\\2)[^\\\\\\r\\n])*\\2)/,\n      inside: {\n        attribute: /^@\\w+/,\n        string: /['\"][\\s\\S]+/\n      }\n    },\n    comment: {\n      pattern: /#.*/,\n      greedy: true\n    },\n    // ~r\"\"\"foo\"\"\" (multi-line), ~r'''foo''' (multi-line), ~r/foo/, ~r|foo|, ~r\"foo\", ~r'foo', ~r(foo), ~r[foo], ~r{foo}, ~r<foo>\n    regex: {\n      pattern:\n        /~[rR](?:(\"\"\"|''')(?:\\\\[\\s\\S]|(?!\\1)[^\\\\])+\\1|([\\/|\"'])(?:\\\\.|(?!\\2)[^\\\\\\r\\n])+\\2|\\((?:\\\\.|[^\\\\)\\r\\n])+\\)|\\[(?:\\\\.|[^\\\\\\]\\r\\n])+\\]|\\{(?:\\\\.|[^\\\\}\\r\\n])+\\}|<(?:\\\\.|[^\\\\>\\r\\n])+>)[uismxfr]*/,\n      greedy: true\n    },\n    string: [\n      {\n        // ~s\"\"\"foo\"\"\" (multi-line), ~s'''foo''' (multi-line), ~s/foo/, ~s|foo|, ~s\"foo\", ~s'foo', ~s(foo), ~s[foo], ~s{foo} (with interpolation care), ~s<foo>\n        pattern:\n          /~[cCsSwW](?:(\"\"\"|''')(?:\\\\[\\s\\S]|(?!\\1)[^\\\\])+\\1|([\\/|\"'])(?:\\\\.|(?!\\2)[^\\\\\\r\\n])+\\2|\\((?:\\\\.|[^\\\\)\\r\\n])+\\)|\\[(?:\\\\.|[^\\\\\\]\\r\\n])+\\]|\\{(?:\\\\.|#\\{[^}]+\\}|#(?!\\{)|[^#\\\\}\\r\\n])+\\}|<(?:\\\\.|[^\\\\>\\r\\n])+>)[csa]?/,\n        greedy: true,\n        inside: {\n          // See interpolation below\n        }\n      },\n      {\n        pattern: /(\"\"\"|''')[\\s\\S]*?\\1/,\n        greedy: true,\n        inside: {\n          // See interpolation below\n        }\n      },\n      {\n        // Multi-line strings are allowed\n        pattern: /(\"|')(?:\\\\(?:\\r\\n|[\\s\\S])|(?!\\1)[^\\\\\\r\\n])*\\1/,\n        greedy: true,\n        inside: {\n          // See interpolation below\n        }\n      }\n    ],\n    atom: {\n      // Look-behind prevents bad highlighting of the :: operator\n      pattern: /(^|[^:]):\\w+/,\n      lookbehind: true,\n      alias: 'symbol'\n    },\n    module: {\n      pattern: /\\b[A-Z]\\w*\\b/,\n      alias: 'class-name'\n    },\n    // Look-ahead prevents bad highlighting of the :: operator\n    'attr-name': /\\b\\w+\\??:(?!:)/,\n    argument: {\n      // Look-behind prevents bad highlighting of the && operator\n      pattern: /(^|[^&])&\\d+/,\n      lookbehind: true,\n      alias: 'variable'\n    },\n    attribute: {\n      pattern: /@\\w+/,\n      alias: 'variable'\n    },\n    function: /\\b[_a-zA-Z]\\w*[?!]?(?:(?=\\s*(?:\\.\\s*)?\\()|(?=\\/\\d))/,\n    number: /\\b(?:0[box][a-f\\d_]+|\\d[\\d_]*)(?:\\.[\\d_]+)?(?:e[+-]?[\\d_]+)?\\b/i,\n    keyword:\n      /\\b(?:after|alias|and|case|catch|cond|def(?:callback|delegate|exception|impl|macro|module|n|np|p|protocol|struct)?|do|else|end|fn|for|if|import|not|or|quote|raise|require|rescue|try|unless|unquote|use|when)\\b/,\n    boolean: /\\b(?:false|nil|true)\\b/,\n    operator: [\n      /\\bin\\b|&&?|\\|[|>]?|\\\\\\\\|::|\\.\\.\\.?|\\+\\+?|-[->]?|<[-=>]|>=|!==?|\\B!|=(?:==?|[>~])?|[*\\/^]/,\n      {\n        // We don't want to match <<\n        pattern: /([^<])<(?!<)/,\n        lookbehind: true\n      },\n      {\n        // We don't want to match >>\n        pattern: /([^>])>(?!>)/,\n        lookbehind: true\n      }\n    ],\n    punctuation: /<<|>>|[.,%\\[\\]{}()]/\n  }\n  Prism.languages.elixir.string.forEach(function (o) {\n    o.inside = {\n      interpolation: {\n        pattern: /#\\{[^}]+\\}/,\n        inside: {\n          delimiter: {\n            pattern: /^#\\{|\\}$/,\n            alias: 'punctuation'\n          },\n          rest: Prism.languages.elixir\n        }\n      }\n    }\n  })\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,WAAO,UAAU;AACjB,WAAO,cAAc;AACrB,WAAO,UAAU,CAAC;AAClB,aAAS,OAAO,OAAO;AACrB,YAAM,UAAU,SAAS;AAAA,QACvB,KAAK;AAAA,UACH,SACE;AAAA,UACF,QAAQ;AAAA,YACN,WAAW;AAAA,YACX,QAAQ;AAAA,UACV;AAAA,QACF;AAAA,QACA,SAAS;AAAA,UACP,SAAS;AAAA,UACT,QAAQ;AAAA,QACV;AAAA;AAAA,QAEA,OAAO;AAAA,UACL,SACE;AAAA,UACF,QAAQ;AAAA,QACV;AAAA,QACA,QAAQ;AAAA,UACN;AAAA;AAAA,YAEE,SACE;AAAA,YACF,QAAQ;AAAA,YACR,QAAQ;AAAA;AAAA,YAER;AAAA,UACF;AAAA,UACA;AAAA,YACE,SAAS;AAAA,YACT,QAAQ;AAAA,YACR,QAAQ;AAAA;AAAA,YAER;AAAA,UACF;AAAA,UACA;AAAA;AAAA,YAEE,SAAS;AAAA,YACT,QAAQ;AAAA,YACR,QAAQ;AAAA;AAAA,YAER;AAAA,UACF;AAAA,QACF;AAAA,QACA,MAAM;AAAA;AAAA,UAEJ,SAAS;AAAA,UACT,YAAY;AAAA,UACZ,OAAO;AAAA,QACT;AAAA,QACA,QAAQ;AAAA,UACN,SAAS;AAAA,UACT,OAAO;AAAA,QACT;AAAA;AAAA,QAEA,aAAa;AAAA,QACb,UAAU;AAAA;AAAA,UAER,SAAS;AAAA,UACT,YAAY;AAAA,UACZ,OAAO;AAAA,QACT;AAAA,QACA,WAAW;AAAA,UACT,SAAS;AAAA,UACT,OAAO;AAAA,QACT;AAAA,QACA,UAAU;AAAA,QACV,QAAQ;AAAA,QACR,SACE;AAAA,QACF,SAAS;AAAA,QACT,UAAU;AAAA,UACR;AAAA,UACA;AAAA;AAAA,YAEE,SAAS;AAAA,YACT,YAAY;AAAA,UACd;AAAA,UACA;AAAA;AAAA,YAEE,SAAS;AAAA,YACT,YAAY;AAAA,UACd;AAAA,QACF;AAAA,QACA,aAAa;AAAA,MACf;AACA,YAAM,UAAU,OAAO,OAAO,QAAQ,SAAU,GAAG;AACjD,UAAE,SAAS;AAAA,UACT,eAAe;AAAA,YACb,SAAS;AAAA,YACT,QAAQ;AAAA,cACN,WAAW;AAAA,gBACT,SAAS;AAAA,gBACT,OAAO;AAAA,cACT;AAAA,cACA,MAAM,MAAM,UAAU;AAAA,YACxB;AAAA,UACF;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH;AAAA;AAAA;", "names": []}