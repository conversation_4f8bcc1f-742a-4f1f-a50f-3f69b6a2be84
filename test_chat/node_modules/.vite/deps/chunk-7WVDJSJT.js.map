{"version": 3, "sources": ["../../refractor/lang/batch.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = batch\nbatch.displayName = 'batch'\nbatch.aliases = []\nfunction batch(Prism) {\n  ;(function (Prism) {\n    var variable = /%%?[~:\\w]+%?|!\\S+!/\n    var parameter = {\n      pattern: /\\/[a-z?]+(?=[ :]|$):?|-[a-z]\\b|--[a-z-]+\\b/im,\n      alias: 'attr-name',\n      inside: {\n        punctuation: /:/\n      }\n    }\n    var string = /\"(?:[\\\\\"]\"|[^\"])*\"(?!\")/\n    var number = /(?:\\b|-)\\d+\\b/\n    Prism.languages.batch = {\n      comment: [\n        /^::.*/m,\n        {\n          pattern: /((?:^|[&(])[ \\t]*)rem\\b(?:[^^&)\\r\\n]|\\^(?:\\r\\n|[\\s\\S]))*/im,\n          lookbehind: true\n        }\n      ],\n      label: {\n        pattern: /^:.*/m,\n        alias: 'property'\n      },\n      command: [\n        {\n          // FOR command\n          pattern:\n            /((?:^|[&(])[ \\t]*)for(?: \\/[a-z?](?:[ :](?:\"[^\"]*\"|[^\\s\"/]\\S*))?)* \\S+ in \\([^)]+\\) do/im,\n          lookbehind: true,\n          inside: {\n            keyword: /\\b(?:do|in)\\b|^for\\b/i,\n            string: string,\n            parameter: parameter,\n            variable: variable,\n            number: number,\n            punctuation: /[()',]/\n          }\n        },\n        {\n          // IF command\n          pattern:\n            /((?:^|[&(])[ \\t]*)if(?: \\/[a-z?](?:[ :](?:\"[^\"]*\"|[^\\s\"/]\\S*))?)* (?:not )?(?:cmdextversion \\d+|defined \\w+|errorlevel \\d+|exist \\S+|(?:\"[^\"]*\"|(?!\")(?:(?!==)\\S)+)?(?:==| (?:equ|geq|gtr|leq|lss|neq) )(?:\"[^\"]*\"|[^\\s\"]\\S*))/im,\n          lookbehind: true,\n          inside: {\n            keyword:\n              /\\b(?:cmdextversion|defined|errorlevel|exist|not)\\b|^if\\b/i,\n            string: string,\n            parameter: parameter,\n            variable: variable,\n            number: number,\n            operator: /\\^|==|\\b(?:equ|geq|gtr|leq|lss|neq)\\b/i\n          }\n        },\n        {\n          // ELSE command\n          pattern: /((?:^|[&()])[ \\t]*)else\\b/im,\n          lookbehind: true,\n          inside: {\n            keyword: /^else\\b/i\n          }\n        },\n        {\n          // SET command\n          pattern:\n            /((?:^|[&(])[ \\t]*)set(?: \\/[a-z](?:[ :](?:\"[^\"]*\"|[^\\s\"/]\\S*))?)* (?:[^^&)\\r\\n]|\\^(?:\\r\\n|[\\s\\S]))*/im,\n          lookbehind: true,\n          inside: {\n            keyword: /^set\\b/i,\n            string: string,\n            parameter: parameter,\n            variable: [variable, /\\w+(?=(?:[*\\/%+\\-&^|]|<<|>>)?=)/],\n            number: number,\n            operator: /[*\\/%+\\-&^|]=?|<<=?|>>=?|[!~_=]/,\n            punctuation: /[()',]/\n          }\n        },\n        {\n          // Other commands\n          pattern:\n            /((?:^|[&(])[ \\t]*@?)\\w+\\b(?:\"(?:[\\\\\"]\"|[^\"])*\"(?!\")|[^\"^&)\\r\\n]|\\^(?:\\r\\n|[\\s\\S]))*/m,\n          lookbehind: true,\n          inside: {\n            keyword: /^\\w+\\b/,\n            string: string,\n            parameter: parameter,\n            label: {\n              pattern: /(^\\s*):\\S+/m,\n              lookbehind: true,\n              alias: 'property'\n            },\n            variable: variable,\n            number: number,\n            operator: /\\^/\n          }\n        }\n      ],\n      operator: /[&@]/,\n      punctuation: /[()']/\n    }\n  })(Prism)\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,WAAO,UAAU;AACjB,UAAM,cAAc;AACpB,UAAM,UAAU,CAAC;AACjB,aAAS,MAAM,OAAO;AACpB;AAAC,OAAC,SAAUA,QAAO;AACjB,YAAI,WAAW;AACf,YAAI,YAAY;AAAA,UACd,SAAS;AAAA,UACT,OAAO;AAAA,UACP,QAAQ;AAAA,YACN,aAAa;AAAA,UACf;AAAA,QACF;AACA,YAAI,SAAS;AACb,YAAI,SAAS;AACb,QAAAA,OAAM,UAAU,QAAQ;AAAA,UACtB,SAAS;AAAA,YACP;AAAA,YACA;AAAA,cACE,SAAS;AAAA,cACT,YAAY;AAAA,YACd;AAAA,UACF;AAAA,UACA,OAAO;AAAA,YACL,SAAS;AAAA,YACT,OAAO;AAAA,UACT;AAAA,UACA,SAAS;AAAA,YACP;AAAA;AAAA,cAEE,SACE;AAAA,cACF,YAAY;AAAA,cACZ,QAAQ;AAAA,gBACN,SAAS;AAAA,gBACT;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA,aAAa;AAAA,cACf;AAAA,YACF;AAAA,YACA;AAAA;AAAA,cAEE,SACE;AAAA,cACF,YAAY;AAAA,cACZ,QAAQ;AAAA,gBACN,SACE;AAAA,gBACF;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA,UAAU;AAAA,cACZ;AAAA,YACF;AAAA,YACA;AAAA;AAAA,cAEE,SAAS;AAAA,cACT,YAAY;AAAA,cACZ,QAAQ;AAAA,gBACN,SAAS;AAAA,cACX;AAAA,YACF;AAAA,YACA;AAAA;AAAA,cAEE,SACE;AAAA,cACF,YAAY;AAAA,cACZ,QAAQ;AAAA,gBACN,SAAS;AAAA,gBACT;AAAA,gBACA;AAAA,gBACA,UAAU,CAAC,UAAU,iCAAiC;AAAA,gBACtD;AAAA,gBACA,UAAU;AAAA,gBACV,aAAa;AAAA,cACf;AAAA,YACF;AAAA,YACA;AAAA;AAAA,cAEE,SACE;AAAA,cACF,YAAY;AAAA,cACZ,QAAQ;AAAA,gBACN,SAAS;AAAA,gBACT;AAAA,gBACA;AAAA,gBACA,OAAO;AAAA,kBACL,SAAS;AAAA,kBACT,YAAY;AAAA,kBACZ,OAAO;AAAA,gBACT;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA,UAAU;AAAA,cACZ;AAAA,YACF;AAAA,UACF;AAAA,UACA,UAAU;AAAA,UACV,aAAa;AAAA,QACf;AAAA,MACF,GAAG,KAAK;AAAA,IACV;AAAA;AAAA;", "names": ["Prism"]}