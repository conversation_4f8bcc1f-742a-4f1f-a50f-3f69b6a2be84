{"version": 3, "sources": ["../../refractor/lang/sparql.js"], "sourcesContent": ["'use strict'\nvar refractorTurtle = require('./turtle.js')\nmodule.exports = sparql\nsparql.displayName = 'sparql'\nsparql.aliases = ['rq']\nfunction sparql(Prism) {\n  Prism.register(refractorTurtle)\n  Prism.languages.sparql = Prism.languages.extend('turtle', {\n    boolean: /\\b(?:false|true)\\b/i,\n    variable: {\n      pattern: /[?$]\\w+/,\n      greedy: true\n    }\n  })\n  Prism.languages.insertBefore('sparql', 'punctuation', {\n    keyword: [\n      /\\b(?:A|ADD|ALL|AS|ASC|ASK|BNODE|BY|CLEAR|CONSTRUCT|COPY|CREATE|DATA|DEFAULT|DELETE|DESC|DESCRIBE|DISTINCT|DROP|EXISTS|FILTER|FROM|GROUP|HAVING|INSERT|INTO|LIMIT|LOAD|MINUS|MOVE|NAMED|NOT|NOW|OFFSET|OPTIONAL|ORDER|RAND|REDUCED|SELECT|SEPARATOR|SERVICE|SILENT|STRUUID|UNION|USING|UUID|VALUES|WHERE)\\b/i,\n      /\\b(?:ABS|AVG|BIND|BOUND|CEIL|COALESCE|CONCAT|CONTAINS|COUNT|DATATYPE|DAY|ENCODE_FOR_URI|FLOOR|GROUP_CONCAT|HOURS|IF|IRI|isBLANK|isIRI|isLITERAL|isNUMERIC|isURI|LANG|LANGMATCHES|LCASE|MAX|MD5|MIN|MINUTES|MONTH|REGEX|REPLACE|ROUND|sameTerm|SAMPLE|SECONDS|SHA1|SHA256|SHA384|SHA512|STR|STRAFTER|STRBEFORE|STRDT|STRENDS|STRLANG|STRLEN|STRSTARTS|SUBSTR|SUM|TIMEZONE|TZ|UCASE|URI|YEAR)\\b(?=\\s*\\()/i,\n      /\\b(?:BASE|GRAPH|PREFIX)\\b/i\n    ]\n  })\n  Prism.languages.rq = Prism.languages.sparql\n}\n"], "mappings": ";;;;;;;;AAAA;AAAA;AACA,QAAI,kBAAkB;AACtB,WAAO,UAAU;AACjB,WAAO,cAAc;AACrB,WAAO,UAAU,CAAC,IAAI;AACtB,aAAS,OAAO,OAAO;AACrB,YAAM,SAAS,eAAe;AAC9B,YAAM,UAAU,SAAS,MAAM,UAAU,OAAO,UAAU;AAAA,QACxD,SAAS;AAAA,QACT,UAAU;AAAA,UACR,SAAS;AAAA,UACT,QAAQ;AAAA,QACV;AAAA,MACF,CAAC;AACD,YAAM,UAAU,aAAa,UAAU,eAAe;AAAA,QACpD,SAAS;AAAA,UACP;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,MACF,CAAC;AACD,YAAM,UAAU,KAAK,MAAM,UAAU;AAAA,IACvC;AAAA;AAAA;", "names": []}