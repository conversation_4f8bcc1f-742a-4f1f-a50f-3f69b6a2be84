{"version": 3, "sources": ["../../refractor/lang/fortran.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = fortran\nfortran.displayName = 'fortran'\nfortran.aliases = []\nfunction fortran(Prism) {\n  Prism.languages.fortran = {\n    'quoted-number': {\n      pattern: /[BOZ](['\"])[A-F0-9]+\\1/i,\n      alias: 'number'\n    },\n    string: {\n      pattern:\n        /(?:\\b\\w+_)?(['\"])(?:\\1\\1|&(?:\\r\\n?|\\n)(?:[ \\t]*!.*(?:\\r\\n?|\\n)|(?![ \\t]*!))|(?!\\1).)*(?:\\1|&)/,\n      inside: {\n        comment: {\n          pattern: /(&(?:\\r\\n?|\\n)\\s*)!.*/,\n          lookbehind: true\n        }\n      }\n    },\n    comment: {\n      pattern: /!.*/,\n      greedy: true\n    },\n    boolean: /\\.(?:FALSE|TRUE)\\.(?:_\\w+)?/i,\n    number: /(?:\\b\\d+(?:\\.\\d*)?|\\B\\.\\d+)(?:[ED][+-]?\\d+)?(?:_\\w+)?/i,\n    keyword: [\n      // Types\n      /\\b(?:CHARACTER|COMPLEX|DOUBLE ?PRECISION|INTEGER|LOGICAL|REAL)\\b/i, // END statements\n      /\\b(?:END ?)?(?:BLOCK ?DATA|DO|FILE|FORALL|FUNCTION|IF|INTERFACE|MODULE(?! PROCEDURE)|PROGRAM|SELECT|SUBROUTINE|TYPE|WHERE)\\b/i, // Statements\n      /\\b(?:ALLOCATABLE|ALLOCATE|BACKSPACE|CALL|CASE|CLOSE|COMMON|CONTAINS|CONTINUE|CYCLE|DATA|DEALLOCATE|DIMENSION|DO|END|EQUIVALENCE|EXIT|EXTERNAL|FORMAT|GO ?TO|IMPLICIT(?: NONE)?|INQUIRE|INTENT|INTRINSIC|MODULE PROCEDURE|NAMELIST|NULLIFY|OPEN|OPTIONAL|PARAMETER|POINTER|PRINT|PRIVATE|PUBLIC|READ|RETURN|REWIND|SAVE|SELECT|STOP|TARGET|WHILE|WRITE)\\b/i, // Others\n      /\\b(?:ASSIGNMENT|DEFAULT|ELEMENTAL|ELSE|ELSEIF|ELSEWHERE|ENTRY|IN|INCLUDE|INOUT|KIND|NULL|ONLY|OPERATOR|OUT|PURE|RECURSIVE|RESULT|SEQUENCE|STAT|THEN|USE)\\b/i\n    ],\n    operator: [\n      /\\*\\*|\\/\\/|=>|[=\\/]=|[<>]=?|::|[+\\-*=%]|\\.[A-Z]+\\./i,\n      {\n        // Use lookbehind to prevent confusion with (/ /)\n        pattern: /(^|(?!\\().)\\/(?!\\))/,\n        lookbehind: true\n      }\n    ],\n    punctuation: /\\(\\/|\\/\\)|[(),;:&]/\n  }\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,WAAO,UAAU;AACjB,YAAQ,cAAc;AACtB,YAAQ,UAAU,CAAC;AACnB,aAAS,QAAQ,OAAO;AACtB,YAAM,UAAU,UAAU;AAAA,QACxB,iBAAiB;AAAA,UACf,SAAS;AAAA,UACT,OAAO;AAAA,QACT;AAAA,QACA,QAAQ;AAAA,UACN,SACE;AAAA,UACF,QAAQ;AAAA,YACN,SAAS;AAAA,cACP,SAAS;AAAA,cACT,YAAY;AAAA,YACd;AAAA,UACF;AAAA,QACF;AAAA,QACA,SAAS;AAAA,UACP,SAAS;AAAA,UACT,QAAQ;AAAA,QACV;AAAA,QACA,SAAS;AAAA,QACT,QAAQ;AAAA,QACR,SAAS;AAAA;AAAA,UAEP;AAAA;AAAA,UACA;AAAA;AAAA,UACA;AAAA;AAAA,UACA;AAAA,QACF;AAAA,QACA,UAAU;AAAA,UACR;AAAA,UACA;AAAA;AAAA,YAEE,SAAS;AAAA,YACT,YAAY;AAAA,UACd;AAAA,QACF;AAAA,QACA,aAAa;AAAA,MACf;AAAA,IACF;AAAA;AAAA;", "names": []}