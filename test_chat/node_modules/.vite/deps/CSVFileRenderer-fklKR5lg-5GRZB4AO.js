import {
  SvgCopy
} from "./chunk-Q5F2TXMV.js";
import "./chunk-GW7ZLH4I.js";
import "./chunk-H5LBIMSD.js";
import "./chunk-RXQNCEQM.js";
import "./chunk-ETT4CMN3.js";
import "./chunk-NYVY23WB.js";
import "./chunk-OYTLD4UU.js";
import "./chunk-N327FID7.js";
import "./chunk-UQ2E6EY3.js";
import "./chunk-Q742DFIA.js";
import "./chunk-L4GHOZCI.js";
import "./chunk-F6S3XU4Q.js";
import "./chunk-ISJNLGRJ.js";
import "./chunk-PSIUHI32.js";
import "./chunk-7JK7ZRL5.js";
import "./chunk-5NLMFLCF.js";
import "./chunk-JAGV2TJD.js";
import "./chunk-ZVPRMJV2.js";
import "./chunk-DJ7ATH4N.js";
import "./chunk-H62SYTQU.js";
import "./chunk-LMAFSY2R.js";
import "./chunk-IJPXWVH3.js";
import "./chunk-45QVFZOB.js";
import "./chunk-JXKUI4B7.js";
import "./chunk-WTXLYQF4.js";
import "./chunk-K352ERNO.js";
import "./chunk-FMDV5RCK.js";
import "./chunk-7VMANK5L.js";
import "./chunk-URUJOSUJ.js";
import "./chunk-RBVYGCVD.js";
import "./chunk-M3KPONMB.js";
import "./chunk-ALDDERU2.js";
import "./chunk-RH7JINPN.js";
import "./chunk-TTCB42LP.js";
import "./chunk-L5VFTYPX.js";
import "./chunk-JF4PEHHI.js";
import "./chunk-IA7T2MXR.js";
import "./chunk-VZYT77PZ.js";
import "./chunk-E2KY547C.js";
import "./chunk-4KWXGKFP.js";
import "./chunk-UURJD6NS.js";
import "./chunk-BJJPXBS3.js";
import "./chunk-5IV5VSTI.js";
import "./chunk-YMZPUAJG.js";
import "./chunk-URACKOSR.js";
import "./chunk-QYFUKD7D.js";
import "./chunk-AXN5CREW.js";
import "./chunk-QPW2WK6A.js";
import "./chunk-5XIHPGFN.js";
import "./chunk-PAZ2QJRG.js";
import "./chunk-2WLIC23G.js";
import "./chunk-WKNQGVOA.js";
import "./chunk-3G4GP7YO.js";
import "./chunk-XOKGFMVE.js";
import "./chunk-QGGOPY2B.js";
import "./chunk-745FJQNS.js";
import "./chunk-JTHKXIFG.js";
import "./chunk-GNR4RKRZ.js";
import "./chunk-4XYKQC4B.js";
import "./chunk-GO54NCRR.js";
import "./chunk-27X7CGDF.js";
import "./chunk-5VXVS7AR.js";
import "./chunk-ZC26MDXA.js";
import "./chunk-7GVYX4OO.js";
import "./chunk-FNA5OTXB.js";
import "./chunk-PHYPWFDC.js";
import "./chunk-3AVRGSRD.js";
import "./chunk-IBFVDTXA.js";
import "./chunk-5IWKE7HL.js";
import "./chunk-L5UAMXRI.js";
import "./chunk-QPLGVDOZ.js";
import "./chunk-ZZP6QNJN.js";
import "./chunk-DMHDSZYL.js";
import "./chunk-ULOI4K2V.js";
import "./chunk-3TIWLMIM.js";
import "./chunk-MVQLPJ7I.js";
import "./chunk-OMXDSNE3.js";
import "./chunk-AUGJGPAT.js";
import "./chunk-UFDSBAMS.js";
import "./chunk-RF743XW4.js";
import "./chunk-PMJSNCLS.js";
import "./chunk-PUT4HYWR.js";
import "./chunk-CYS4FYUN.js";
import "./chunk-V6UFGXAH.js";
import "./chunk-255DYT6C.js";
import "./chunk-AIH4VHUY.js";
import "./chunk-J7RPM3DH.js";
import "./chunk-5LJBMLVS.js";
import "./chunk-QOQRLVRR.js";
import "./chunk-6DH244VY.js";
import "./chunk-6D5DW2FW.js";
import "./chunk-S276KH6Q.js";
import "./chunk-YG2GDG2Z.js";
import "./chunk-CGAWJQY7.js";
import "./chunk-GPZEY5WB.js";
import "./chunk-32MM5ZX4.js";
import "./chunk-7LCD4BP4.js";
import "./chunk-SLQ7PGLD.js";
import "./chunk-DLIXEZJS.js";
import "./chunk-NVK4QYID.js";
import "./chunk-2XSKG55U.js";
import "./chunk-T34TJ6IT.js";
import "./chunk-H7B4CTBK.js";
import "./chunk-LVLKDONZ.js";
import "./chunk-XTG6M6R6.js";
import "./chunk-BGQ54XF5.js";
import "./chunk-5L4VHEFN.js";
import "./chunk-5KFIYGDA.js";
import "./chunk-BRQUGG4O.js";
import "./chunk-UV3TBD27.js";
import "./chunk-QDZRGMZX.js";
import "./chunk-L6KZIFJV.js";
import "./chunk-F423HY2E.js";
import "./chunk-H7EUDSG3.js";
import "./chunk-QXY27HF2.js";
import "./chunk-OIP4M7WN.js";
import "./chunk-7FXP27JW.js";
import "./chunk-XIKTR56D.js";
import "./chunk-JKHA3JVS.js";
import "./chunk-JKOVUWNZ.js";
import "./chunk-CSO6OS4O.js";
import "./chunk-EZU75OMO.js";
import "./chunk-CZ6RDS6V.js";
import "./chunk-GI72WR77.js";
import "./chunk-PAV2RHSN.js";
import "./chunk-EERZ2PT3.js";
import "./chunk-6WFL773Z.js";
import "./chunk-KWPWPFL2.js";
import "./chunk-NHJBNPII.js";
import "./chunk-GJGDQPF7.js";
import "./chunk-B3CIRX7U.js";
import "./chunk-4KWPWMXH.js";
import "./chunk-4KWEC4LS.js";
import "./chunk-M3MYNRY2.js";
import "./chunk-4LVOO47C.js";
import "./chunk-G6RNX5PA.js";
import "./chunk-F7AYLA72.js";
import "./chunk-C7OV2ZWZ.js";
import "./chunk-WHS3ZVQ7.js";
import "./chunk-23AC4FZO.js";
import "./chunk-NKME456S.js";
import "./chunk-GVSIUJSX.js";
import "./chunk-Q323JI4Z.js";
import "./chunk-NXHWN3AU.js";
import "./chunk-QSUA327X.js";
import "./chunk-6MQV7GXH.js";
import "./chunk-376XAWBK.js";
import "./chunk-DTHN3HYC.js";
import "./chunk-IBT7FFNF.js";
import "./chunk-4KDC3GRE.js";
import "./chunk-YT5LVHR2.js";
import "./chunk-MATDH4H3.js";
import "./chunk-BDYCJY4O.js";
import "./chunk-5XLF2V3M.js";
import "./chunk-VO5ZPTYY.js";
import "./chunk-D7IMIYKZ.js";
import "./chunk-JH7AFGHP.js";
import "./chunk-KJVKMJVY.js";
import "./chunk-D532R36F.js";
import "./chunk-7AJ4E6JZ.js";
import "./chunk-6AFQDJQW.js";
import "./chunk-A5JBH6TK.js";
import "./chunk-B5OI43CQ.js";
import "./chunk-HVGMMH4U.js";
import "./chunk-SHLWMB6K.js";
import "./chunk-YSHDUHNT.js";
import "./chunk-FPFWYE3Z.js";
import "./chunk-YWRON2GZ.js";
import "./chunk-OBUJPQ3E.js";
import "./chunk-HF6KWTNT.js";
import "./chunk-7SR3URHT.js";
import "./chunk-RL6WF6YS.js";
import "./chunk-EHKALW3P.js";
import "./chunk-WFBKFZE6.js";
import "./chunk-5M7NSRNH.js";
import "./chunk-VQM6ATLP.js";
import "./chunk-43K4JMIQ.js";
import "./chunk-HJRQV3HW.js";
import "./chunk-EI5FZLGD.js";
import "./chunk-3RHSRLNX.js";
import "./chunk-INV3MNKO.js";
import "./chunk-GUZIO3N5.js";
import "./chunk-HUN4DCRY.js";
import "./chunk-YCMRBZQA.js";
import "./chunk-PNSJ3D2M.js";
import "./chunk-LOYJP7FG.js";
import "./chunk-7AXOADLE.js";
import "./chunk-2O4TYYM5.js";
import "./chunk-BSXWLQER.js";
import "./chunk-XWJ7Z47A.js";
import "./chunk-CNSIRLZT.js";
import "./chunk-MU3GZRS5.js";
import "./chunk-CJCKXIOD.js";
import "./chunk-JI7Y4XND.js";
import "./chunk-UW7R5ZU6.js";
import "./chunk-E5QKWPJG.js";
import "./chunk-MWAD63XE.js";
import "./chunk-7TM37DPX.js";
import "./chunk-NQVVJI7J.js";
import "./chunk-YJVQZKKP.js";
import "./chunk-XZI77OLY.js";
import "./chunk-KCI6OK3S.js";
import "./chunk-N3XWDO2D.js";
import "./chunk-NPH7QPSU.js";
import "./chunk-U7RXEW4Y.js";
import "./chunk-VWZOMI2Q.js";
import "./chunk-ODF6R76H.js";
import "./chunk-DX6HXGS2.js";
import "./chunk-UC6UK4LK.js";
import "./chunk-Q43XPOZ5.js";
import "./chunk-2EDMOEFB.js";
import "./chunk-T64VCLBP.js";
import "./chunk-553QSIPC.js";
import "./chunk-3NNIH3JE.js";
import "./chunk-OPBEWIXO.js";
import "./chunk-SCBBK3IN.js";
import "./chunk-PWAS444Q.js";
import "./chunk-Z2MIROZJ.js";
import "./chunk-Y4LVORRF.js";
import "./chunk-WY26ZMJF.js";
import "./chunk-ICOTX67I.js";
import "./chunk-T3IPAXQI.js";
import "./chunk-PZLAVEGZ.js";
import "./chunk-XGKJZXKB.js";
import "./chunk-2S4X26MU.js";
import "./chunk-3H2AAJ46.js";
import "./chunk-CE7HWCP3.js";
import "./chunk-QYGSHQPC.js";
import "./chunk-VOH3JTDD.js";
import "./chunk-Q4EBSTYJ.js";
import "./chunk-QCEJB3F2.js";
import "./chunk-VHEGBLMV.js";
import "./chunk-3WR552XK.js";
import "./chunk-INRORV4J.js";
import "./chunk-ROCK2QBB.js";
import "./chunk-QJNIVSAK.js";
import "./chunk-74JIZER2.js";
import "./chunk-OA4Y3SPH.js";
import "./chunk-CQ2J2ZOZ.js";
import "./chunk-MR7DJFF7.js";
import "./chunk-UJ2KEEO6.js";
import "./chunk-DQSX3ZYV.js";
import "./chunk-ECSBSNKY.js";
import "./chunk-SQLTRR2F.js";
import "./chunk-MF73BJTW.js";
import "./chunk-FMTXOJXR.js";
import "./chunk-ISRJB4JU.js";
import "./chunk-KN3SQONT.js";
import "./chunk-NT475Y4I.js";
import "./chunk-GZRQMMCR.js";
import "./chunk-7WVDJSJT.js";
import "./chunk-25JHHCOX.js";
import "./chunk-6WUSYZTS.js";
import "./chunk-NQQJK7SP.js";
import "./chunk-P6AR7BTZ.js";
import "./chunk-66ZBYU3F.js";
import "./chunk-GECZHEWB.js";
import "./chunk-X357WTVP.js";
import "./chunk-IHY6IM42.js";
import "./chunk-UEW6EHPX.js";
import "./chunk-IZZTLFSO.js";
import "./chunk-3PI5U6CZ.js";
import "./chunk-PPKKEEZS.js";
import "./chunk-YSGK26DE.js";
import "./chunk-NMFUSJTG.js";
import "./chunk-C7G7WSEK.js";
import "./chunk-ED3XOURR.js";
import "./chunk-TYTYEHAA.js";
import "./chunk-XEQ2CVBM.js";
import "./chunk-IC4DWTYF.js";
import "./chunk-Y3B64RBX.js";
import "./chunk-5FVQWTRX.js";
import "./chunk-VBHCZX4D.js";
import "./chunk-4RLZHEOU.js";
import "./chunk-7GRVAFIR.js";
import "./chunk-IMBI6PNJ.js";
import "./chunk-WJPSB32J.js";
import "./chunk-VGVKRNL7.js";
import "./chunk-J73RUTBI.js";
import "./chunk-4AAWC2PH.js";
import "./chunk-GTYRGG4H.js";
import "./chunk-LM2HR2GH.js";
import "./chunk-4VRW5FQO.js";
import "./chunk-MXYAEB2P.js";
import "./chunk-6SDFGGLV.js";
import "./chunk-DSXG3T6B.js";
import "./chunk-2NZSJKR7.js";
import "./chunk-PQCSFM4W.js";
import "./chunk-UVBZO26W.js";
import "./chunk-DQ37WV66.js";
import "./chunk-ELQRGDC5.js";
import "./chunk-63K4DUNM.js";
import "./chunk-O2Z3NNP4.js";
import "./chunk-XEGY26IY.js";
import "./chunk-HLH3AIP2.js";
import "./chunk-VPU523RF.js";
import "./chunk-KTIUT7FH.js";
import "./chunk-TEL6ZVP6.js";
import "./chunk-6PDCBA4R.js";
import "./chunk-DI55MBDB.js";
import "./chunk-KASSWVSF.js";
import "./chunk-NBXJGR43.js";
import "./chunk-24BGWANU.js";
import "./chunk-DQVIPGW3.js";
import "./chunk-YQPFCCT3.js";
import "./chunk-2MPDM52Z.js";
import "./chunk-JVGHQBZH.js";
import "./chunk-4UX5FWDQ.js";
import "./chunk-2VLYJMNT.js";
import "./chunk-OGA5V6EX.js";
import "./chunk-INNHLJVU.js";
import "./chunk-UQW4I2NM.js";
import "./chunk-XUMWLFJL.js";
import "./chunk-W7SO7QP6.js";
import "./chunk-FAJBY3C6.js";
import "./chunk-ZIEQVYXK.js";
import "./chunk-KNERYP76.js";
import "./chunk-XCTK2V2X.js";
import "./chunk-LMTJ6FSD.js";
import "./chunk-6622FLQR.js";
import "./chunk-Y7ROEEXO.js";
import "./chunk-7MCS2EYC.js";
import "./chunk-PD7DZUFZ.js";
import "./chunk-BJHMQHMK.js";
import "./chunk-UHVMKXA5.js";
import "./chunk-EBRMNZ3H.js";
import "./chunk-ETHJPXUB.js";
import "./chunk-CJCN4RSI.js";
import "./chunk-7TQPB5HN.js";
import "./chunk-PCR73RL5.js";
import "./chunk-2TYSJUSP.js";
import "./chunk-JFEJ3SQQ.js";
import "./chunk-GSFDHBZY.js";
import "./chunk-Z63SN3NN.js";
import "./chunk-42ELT6MV.js";
import "./chunk-WRS66SJH.js";
import "./chunk-SC3JOR3I.js";
import "./chunk-3WBXU7Q5.js";
import "./chunk-7E7352KX.js";
import "./chunk-LEA27RVL.js";
import "./chunk-X6FDJ6XM.js";
import "./chunk-OKGVAPA3.js";
import "./chunk-7TU2W7CN.js";
import "./chunk-4HOPKRF4.js";
import "./chunk-7WQ4X4JP.js";
import "./chunk-V7ZELXBI.js";
import "./chunk-HQO7SK7P.js";
import "./chunk-TQ5KRTLI.js";
import "./chunk-4JCBOFKU.js";
import "./chunk-I4S4UIO6.js";
import "./chunk-5ZIXRB7F.js";
import "./chunk-YZNIAW7D.js";
import "./chunk-VIV64M7Z.js";
import "./chunk-AOLI3HQ4.js";
import "./chunk-TD4GAYAL.js";
import "./chunk-I264OCW6.js";
import "./chunk-VOJURB25.js";
import "./chunk-C4EEXURZ.js";
import "./chunk-7WJQKPKD.js";
import "./chunk-SZUZNUZT.js";
import "./chunk-W4AP4OF4.js";
import "./chunk-22NTCPRR.js";
import "./chunk-YFTHZEAK.js";
import "./chunk-R6MU2BT6.js";
import "./chunk-MZZQDK3H.js";
import "./chunk-5CFFWV3A.js";
import "./chunk-N3UXMU5D.js";
import "./chunk-IZJ6SEYX.js";
import "./chunk-S42A66AD.js";
import "./chunk-SOKOWEGS.js";
import "./chunk-GTXGFO6D.js";
import "./chunk-LU7266DC.js";
import "./chunk-JSCMNQPU.js";
import "./chunk-YXJ3WVZQ.js";
import "./chunk-GCIJW23S.js";
import "./chunk-NH56RX7V.js";
import "./chunk-CBCOTGG2.js";
import "./chunk-B2W3FZQH.js";
import "./chunk-6M2WP3D5.js";
import "./chunk-46P2AIXH.js";
import "./chunk-7UEY3AGH.js";
import "./chunk-UFFV33FR.js";
import "./chunk-ZXO4QRO3.js";
import "./chunk-NBXQ6BEE.js";
import "./chunk-UMELBXD5.js";
import "./chunk-ZJ6NS7BS.js";
import "./chunk-GR6TQDOU.js";
import "./chunk-APGYI6HN.js";
import "./chunk-UMIU5N4F.js";
import "./chunk-25V6AEAX.js";
import "./chunk-H6GXJYIL.js";
import "./chunk-NBD63KKL.js";
import "./chunk-UJAV2XN5.js";
import "./chunk-RA63TJTO.js";
import "./chunk-OJQR6E2M.js";
import "./chunk-YFDYLBKB.js";
import "./chunk-WY2XV3I4.js";
import "./chunk-KHUIFVNQ.js";
import "./chunk-QTD3SA7H.js";
import "./chunk-J273ZBMY.js";
import "./chunk-ULIZGMZN.js";
import "./chunk-TUZ3SE7L.js";
import "./chunk-35K6HZ4D.js";
import "./chunk-D46FGFLV.js";
import "./chunk-TIZFDTUZ.js";
import "./chunk-IOAYQFWM.js";
import "./chunk-PYKA6R2J.js";
import "./chunk-E52LDXY2.js";
import "./chunk-PH5GMCAO.js";
import "./chunk-3V7Y3U4F.js";
import "./chunk-YD7JLR2C.js";
import "./chunk-SME3ZVDA.js";
import "./chunk-HQPI25S4.js";
import "./chunk-HKV3KNIG.js";
import "./chunk-VHPSAQVD.js";
import "./chunk-KN5TVGGD.js";
import "./chunk-MCIGWE4R.js";
import "./chunk-6752ZFAM.js";
import "./chunk-QELMXKSS.js";
import "./chunk-XHGDLVTN.js";
import "./chunk-JK2NRZSN.js";
import "./chunk-GDVDPLBH.js";
import "./chunk-JYXAX3CL.js";
import "./chunk-RUIJAM5C.js";
import "./chunk-D6QPI7O3.js";
import "./chunk-AOR74ZBA.js";
import "./chunk-HPRHGMZX.js";
import "./chunk-NAX2EFX5.js";
import "./chunk-OFNMBTGU.js";
import "./chunk-6YBG5VJM.js";
import "./chunk-KK25TA7G.js";
import "./chunk-FEGMHW2H.js";
import "./chunk-LD7HXFQP.js";
import "./chunk-22NLREDR.js";
import "./chunk-FHWMT6W2.js";
import "./chunk-3ZVDLAFD.js";
import "./chunk-ICJM5JGC.js";
import "./chunk-BIPFR766.js";
import "./chunk-YTE4RZML.js";
import "./chunk-PBGF3EMZ.js";
import "./chunk-X4GJD3GK.js";
import "./chunk-KK5D6Z5N.js";
import "./chunk-F2IJ5CCT.js";
import "./chunk-TNULFJU7.js";
import "./chunk-TP5CPPAC.js";
import "./chunk-PYGGJ36K.js";
import "./chunk-4SAHWFRC.js";
import "./chunk-KSJEBYVM.js";
import "./chunk-DNU5WJ2U.js";
import "./chunk-CV3XYK6B.js";
import "./chunk-VTDSGSYI.js";
import "./chunk-OFDHJRAE.js";
import "./chunk-7V77O5XH.js";
import "./chunk-PRCK2GG2.js";
import "./chunk-GN6YKFO3.js";
import "./chunk-FADENH6T.js";
import "./chunk-QD5YNUNY.js";
import "./chunk-MLI4A6J4.js";
import "./chunk-EPWWX5TZ.js";
import "./chunk-DY7J6EGK.js";
import "./chunk-5U4Z4AJQ.js";
import "./chunk-ECJVA6AG.js";
import "./chunk-5HVGIEPE.js";
import "./chunk-Z5HVHWO5.js";
import "./chunk-5PU7TCGZ.js";
import "./chunk-N7EFAXAY.js";
import "./chunk-TNCEEBLO.js";
import "./chunk-THULASSQ.js";
import "./chunk-ARSCGTYJ.js";
import "./chunk-MJJWZ6ZL.js";
import "./chunk-QMFT4UQF.js";
import {
  AnimatePresence,
  IconButton,
  motion
} from "./chunk-SEGG5DGF.js";
import {
  require_jsx_runtime
} from "./chunk-YX33HODG.js";
import "./chunk-NA32P3ZC.js";
import {
  require_react
} from "./chunk-R26XTA6N.js";
import "./chunk-4ZR7ZZYZ.js";
import {
  __toESM
} from "./chunk-PLDDJCW6.js";

// node_modules/reachat/dist/CSVFileRenderer-fklKR5lg.js
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var React = __toESM(require_react(), 1);
var import_react = __toESM(require_react(), 1);
var sanitizeSVGCell = (cell) => {
  const trimmed = cell.trim();
  const escaped = trimmed.replace(/"/g, '""');
  const prefix = /^[=+\-@]/.test(trimmed) ? "'" : "";
  const needsQuotes = /[",\n\r]/.test(escaped) || prefix;
  return needsQuotes ? `"${prefix}${escaped}"` : escaped;
};
var parseCSV = (csvString) => {
  try {
    const rows = csvString.split("\n");
    return rows.map((row) => row.split(",").map((cell) => sanitizeSVGCell(cell)));
  } catch (error) {
    console.error("Error parsing CSV:", error);
    throw new Error("Failed to parse CSV file.");
  }
};
var SvgDownload = (props) => React.createElement("svg", { xmlns: "http://www.w3.org/2000/svg", width: 24, height: 24, viewBox: "0 0 24 24", fill: "none", stroke: "currentColor", strokeWidth: 1, strokeLinecap: "round", strokeLinejoin: "round", className: "lucide lucide-cloud-download", ...props }, React.createElement("path", { d: "M4 14.899A7 7 0 1 1 15.71 8h1.79a4.5 4.5 0 0 1 2.5 8.242" }), React.createElement("path", { d: "M12 12v9" }), React.createElement("path", { d: "m8 17 4 4 4-4" }));
var CSVFileRenderer = ({ name, url, fileIcon }) => {
  const [isLoading, setIsLoading] = (0, import_react.useState)(true);
  const [csvData, setCsvData] = (0, import_react.useState)([]);
  const [error, setError] = (0, import_react.useState)(null);
  const [isModalOpen, setIsModalOpen] = (0, import_react.useState)(false);
  const modalRef = (0, import_react.useRef)(null);
  (0, import_react.useEffect)(() => {
    const fetchCsvData = async () => {
      try {
        setIsLoading(true);
        const response = await fetch(url);
        const data = parseCSV(await response.text());
        setCsvData(data);
      } catch {
        setError("Failed to load CSV file.");
      } finally {
        setIsLoading(false);
      }
    };
    fetchCsvData();
  }, [url]);
  const toggleModal = () => {
    setIsModalOpen((prev) => !prev);
  };
  const handleClickOutside = (event) => {
    if (modalRef.current && !modalRef.current.contains(event.target)) {
      setIsModalOpen(false);
    }
  };
  (0, import_react.useEffect)(() => {
    if (isModalOpen) {
      document.addEventListener("mousedown", handleClickOutside);
    } else {
      document.removeEventListener("mousedown", handleClickOutside);
    }
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isModalOpen]);
  const downloadCSV = () => {
    if (csvData.length === 0) return;
    const csvContent = csvData.map((row) => row.join(",")).join("\n");
    const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
    const url2 = URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.href = url2;
    link.setAttribute("download", `${name || "data"}`);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };
  const renderTable = (data, maxRows) => (0, import_jsx_runtime.jsxs)(
    motion.table,
    {
      layout: true,
      className: "w-full",
      transition: { type: "spring", stiffness: 100, damping: 20 },
      children: [
        (0, import_jsx_runtime.jsx)("thead", { className: "sticky top-0 bg-gray-200 dark:bg-gray-800 z-10", children: (0, import_jsx_runtime.jsxs)("tr", { children: [
          (0, import_jsx_runtime.jsx)("th", { className: "py-4 px-6", children: "#" }),
          data[0].map((header, index) => (0, import_jsx_runtime.jsx)("th", { className: "py-4 px-6", children: header }, `header-${index}`))
        ] }) }),
        (0, import_jsx_runtime.jsx)("tbody", { children: data.slice(1, maxRows).map((row, rowIndex) => (0, import_jsx_runtime.jsxs)(
          "tr",
          {
            className: "border-b border-panel-accent light:border-gray-700 hover:bg-panel-accent hover:light:bg-gray-700/40 transition-colors text-base",
            children: [
              (0, import_jsx_runtime.jsx)("td", { className: "py-4 px-6", children: rowIndex + 1 }),
              row.map((cell, cellIndex) => (0, import_jsx_runtime.jsx)("td", { className: "py-4 px-6", children: cell }, `cell-${rowIndex}-${cellIndex}`))
            ]
          },
          `row-${rowIndex}`
        )) })
      ]
    }
  );
  return (0, import_jsx_runtime.jsxs)("div", { className: "flex flex-col gap-2", children: [
    (0, import_jsx_runtime.jsxs)("div", { className: "flex justify-between items-center gap-4", children: [
      (0, import_jsx_runtime.jsxs)("div", { className: "csv-icon flex items-center", children: [
        fileIcon,
        name && (0, import_jsx_runtime.jsx)("figcaption", { className: "ml-1", children: name })
      ] }),
      (0, import_jsx_runtime.jsxs)("div", { className: "csv-icon flex items-center gap-6", children: [
        (0, import_jsx_runtime.jsx)(IconButton, { size: "small", variant: "text", onClick: downloadCSV, children: (0, import_jsx_runtime.jsx)(SvgDownload, {}) }),
        (0, import_jsx_runtime.jsx)(IconButton, { size: "small", variant: "text", onClick: toggleModal, children: (0, import_jsx_runtime.jsx)(SvgCopy, {}) })
      ] })
    ] }),
    error && (0, import_jsx_runtime.jsx)("div", { className: "error-message", children: error }),
    isLoading && !csvData && (0, import_jsx_runtime.jsx)("div", { className: "text-text-secondary", children: "Loading..." }),
    (0, import_jsx_runtime.jsx)("div", { className: "flex justify-between", children: !error && csvData.length > 0 && renderTable(csvData, 6) }),
    (0, import_jsx_runtime.jsx)(AnimatePresence, { children: isModalOpen && (0, import_jsx_runtime.jsx)(
      motion.div,
      {
        className: "fixed inset-0 bg-black/70 flex justify-center items-center z-50",
        initial: { opacity: 0 },
        animate: { opacity: 1 },
        exit: { opacity: 0 },
        transition: { duration: 0.3 },
        children: (0, import_jsx_runtime.jsx)(
          motion.div,
          {
            ref: modalRef,
            className: "bg-white dark:bg-gray-900 rounded-md w-11/12 h-5/6 overflow-auto",
            initial: { scale: 0.8 },
            animate: { scale: 1 },
            exit: { scale: 0.8 },
            transition: { duration: 0.3 },
            children: !error && csvData.length > 0 && renderTable(csvData)
          }
        )
      }
    ) })
  ] });
};
export {
  CSVFileRenderer as default
};
//# sourceMappingURL=CSVFileRenderer-fklKR5lg-5GRZB4AO.js.map
