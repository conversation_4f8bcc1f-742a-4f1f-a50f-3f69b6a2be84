{"version": 3, "sources": ["../../refractor/lang/ejs.js"], "sourcesContent": ["'use strict'\nvar refractorMarkupTemplating = require('./markup-templating.js')\nmodule.exports = ejs\nejs.displayName = 'ejs'\nejs.aliases = ['eta']\nfunction ejs(Prism) {\n  Prism.register(refractorMarkupTemplating)\n  ;(function (Prism) {\n    Prism.languages.ejs = {\n      delimiter: {\n        pattern: /^<%[-_=]?|[-_]?%>$/,\n        alias: 'punctuation'\n      },\n      comment: /^#[\\s\\S]*/,\n      'language-javascript': {\n        pattern: /[\\s\\S]+/,\n        inside: Prism.languages.javascript\n      }\n    }\n    Prism.hooks.add('before-tokenize', function (env) {\n      var ejsPattern = /<%(?!%)[\\s\\S]+?%>/g\n      Prism.languages['markup-templating'].buildPlaceholders(\n        env,\n        'ejs',\n        ejsPattern\n      )\n    })\n    Prism.hooks.add('after-tokenize', function (env) {\n      Prism.languages['markup-templating'].tokenizePlaceholders(env, 'ejs')\n    })\n    Prism.languages.eta = Prism.languages.ejs\n  })(Prism)\n}\n"], "mappings": ";;;;;;;;AAAA;AAAA;AACA,QAAI,4BAA4B;AAChC,WAAO,UAAU;AACjB,QAAI,cAAc;AAClB,QAAI,UAAU,CAAC,KAAK;AACpB,aAAS,IAAI,OAAO;AAClB,YAAM,SAAS,yBAAyB;AACvC,OAAC,SAAUA,QAAO;AACjB,QAAAA,OAAM,UAAU,MAAM;AAAA,UACpB,WAAW;AAAA,YACT,SAAS;AAAA,YACT,OAAO;AAAA,UACT;AAAA,UACA,SAAS;AAAA,UACT,uBAAuB;AAAA,YACrB,SAAS;AAAA,YACT,QAAQA,OAAM,UAAU;AAAA,UAC1B;AAAA,QACF;AACA,QAAAA,OAAM,MAAM,IAAI,mBAAmB,SAAU,KAAK;AAChD,cAAI,aAAa;AACjB,UAAAA,OAAM,UAAU,mBAAmB,EAAE;AAAA,YACnC;AAAA,YACA;AAAA,YACA;AAAA,UACF;AAAA,QACF,CAAC;AACD,QAAAA,OAAM,MAAM,IAAI,kBAAkB,SAAU,KAAK;AAC/C,UAAAA,OAAM,UAAU,mBAAmB,EAAE,qBAAqB,KAAK,KAAK;AAAA,QACtE,CAAC;AACD,QAAAA,OAAM,UAAU,MAAMA,OAAM,UAAU;AAAA,MACxC,GAAG,KAAK;AAAA,IACV;AAAA;AAAA;", "names": ["Prism"]}