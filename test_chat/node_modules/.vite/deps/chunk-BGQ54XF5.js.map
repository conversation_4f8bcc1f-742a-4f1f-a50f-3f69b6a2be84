{"version": 3, "sources": ["../../refractor/lang/nevod.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = nevod\nnevod.displayName = 'nevod'\nnevod.aliases = []\nfunction nevod(Prism) {\n  Prism.languages.nevod = {\n    comment: /\\/\\/.*|(?:\\/\\*[\\s\\S]*?(?:\\*\\/|$))/,\n    string: {\n      pattern: /(?:\"(?:\"\"|[^\"])*\"(?!\")|'(?:''|[^'])*'(?!'))!?\\*?/,\n      greedy: true,\n      inside: {\n        'string-attrs': /!$|!\\*$|\\*$/\n      }\n    },\n    namespace: {\n      pattern: /(@namespace\\s+)[a-zA-Z0-9\\-.]+(?=\\s*\\{)/,\n      lookbehind: true\n    },\n    pattern: {\n      pattern:\n        /(@pattern\\s+)?#?[a-zA-Z0-9\\-.]+(?:\\s*\\(\\s*(?:~\\s*)?[a-zA-Z0-9\\-.]+\\s*(?:,\\s*(?:~\\s*)?[a-zA-Z0-9\\-.]*)*\\))?(?=\\s*=)/,\n      lookbehind: true,\n      inside: {\n        'pattern-name': {\n          pattern: /^#?[a-zA-Z0-9\\-.]+/,\n          alias: 'class-name'\n        },\n        fields: {\n          pattern: /\\(.*\\)/,\n          inside: {\n            'field-name': {\n              pattern: /[a-zA-Z0-9\\-.]+/,\n              alias: 'variable'\n            },\n            punctuation: /[,()]/,\n            operator: {\n              pattern: /~/,\n              alias: 'field-hidden-mark'\n            }\n          }\n        }\n      }\n    },\n    search: {\n      pattern: /(@search\\s+|#)[a-zA-Z0-9\\-.]+(?:\\.\\*)?(?=\\s*;)/,\n      alias: 'function',\n      lookbehind: true\n    },\n    keyword:\n      /@(?:having|inside|namespace|outside|pattern|require|search|where)\\b/,\n    'standard-pattern': {\n      pattern:\n        /\\b(?:Alpha|AlphaNum|Any|Blank|End|LineBreak|Num|NumAlpha|Punct|Space|Start|Symbol|Word|WordBreak)\\b(?:\\([a-zA-Z0-9\\-.,\\s+]*\\))?/,\n      inside: {\n        'standard-pattern-name': {\n          pattern: /^[a-zA-Z0-9\\-.]+/,\n          alias: 'builtin'\n        },\n        quantifier: {\n          pattern: /\\b\\d+(?:\\s*\\+|\\s*-\\s*\\d+)?(?!\\w)/,\n          alias: 'number'\n        },\n        'standard-pattern-attr': {\n          pattern: /[a-zA-Z0-9\\-.]+/,\n          alias: 'builtin'\n        },\n        punctuation: /[,()]/\n      }\n    },\n    quantifier: {\n      pattern: /\\b\\d+(?:\\s*\\+|\\s*-\\s*\\d+)?(?!\\w)/,\n      alias: 'number'\n    },\n    operator: [\n      {\n        pattern: /=/,\n        alias: 'pattern-def'\n      },\n      {\n        pattern: /&/,\n        alias: 'conjunction'\n      },\n      {\n        pattern: /~/,\n        alias: 'exception'\n      },\n      {\n        pattern: /\\?/,\n        alias: 'optionality'\n      },\n      {\n        pattern: /[[\\]]/,\n        alias: 'repetition'\n      },\n      {\n        pattern: /[{}]/,\n        alias: 'variation'\n      },\n      {\n        pattern: /[+_]/,\n        alias: 'sequence'\n      },\n      {\n        pattern: /\\.{2,3}/,\n        alias: 'span'\n      }\n    ],\n    'field-capture': [\n      {\n        pattern:\n          /([a-zA-Z0-9\\-.]+\\s*\\()\\s*[a-zA-Z0-9\\-.]+\\s*:\\s*[a-zA-Z0-9\\-.]+(?:\\s*,\\s*[a-zA-Z0-9\\-.]+\\s*:\\s*[a-zA-Z0-9\\-.]+)*(?=\\s*\\))/,\n        lookbehind: true,\n        inside: {\n          'field-name': {\n            pattern: /[a-zA-Z0-9\\-.]+/,\n            alias: 'variable'\n          },\n          colon: /:/\n        }\n      },\n      {\n        pattern: /[a-zA-Z0-9\\-.]+\\s*:/,\n        inside: {\n          'field-name': {\n            pattern: /[a-zA-Z0-9\\-.]+/,\n            alias: 'variable'\n          },\n          colon: /:/\n        }\n      }\n    ],\n    punctuation: /[:;,()]/,\n    name: /[a-zA-Z0-9\\-.]+/\n  }\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,WAAO,UAAU;AACjB,UAAM,cAAc;AACpB,UAAM,UAAU,CAAC;AACjB,aAAS,MAAM,OAAO;AACpB,YAAM,UAAU,QAAQ;AAAA,QACtB,SAAS;AAAA,QACT,QAAQ;AAAA,UACN,SAAS;AAAA,UACT,QAAQ;AAAA,UACR,QAAQ;AAAA,YACN,gBAAgB;AAAA,UAClB;AAAA,QACF;AAAA,QACA,WAAW;AAAA,UACT,SAAS;AAAA,UACT,YAAY;AAAA,QACd;AAAA,QACA,SAAS;AAAA,UACP,SACE;AAAA,UACF,YAAY;AAAA,UACZ,QAAQ;AAAA,YACN,gBAAgB;AAAA,cACd,SAAS;AAAA,cACT,OAAO;AAAA,YACT;AAAA,YACA,QAAQ;AAAA,cACN,SAAS;AAAA,cACT,QAAQ;AAAA,gBACN,cAAc;AAAA,kBACZ,SAAS;AAAA,kBACT,OAAO;AAAA,gBACT;AAAA,gBACA,aAAa;AAAA,gBACb,UAAU;AAAA,kBACR,SAAS;AAAA,kBACT,OAAO;AAAA,gBACT;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,QACA,QAAQ;AAAA,UACN,SAAS;AAAA,UACT,OAAO;AAAA,UACP,YAAY;AAAA,QACd;AAAA,QACA,SACE;AAAA,QACF,oBAAoB;AAAA,UAClB,SACE;AAAA,UACF,QAAQ;AAAA,YACN,yBAAyB;AAAA,cACvB,SAAS;AAAA,cACT,OAAO;AAAA,YACT;AAAA,YACA,YAAY;AAAA,cACV,SAAS;AAAA,cACT,OAAO;AAAA,YACT;AAAA,YACA,yBAAyB;AAAA,cACvB,SAAS;AAAA,cACT,OAAO;AAAA,YACT;AAAA,YACA,aAAa;AAAA,UACf;AAAA,QACF;AAAA,QACA,YAAY;AAAA,UACV,SAAS;AAAA,UACT,OAAO;AAAA,QACT;AAAA,QACA,UAAU;AAAA,UACR;AAAA,YACE,SAAS;AAAA,YACT,OAAO;AAAA,UACT;AAAA,UACA;AAAA,YACE,SAAS;AAAA,YACT,OAAO;AAAA,UACT;AAAA,UACA;AAAA,YACE,SAAS;AAAA,YACT,OAAO;AAAA,UACT;AAAA,UACA;AAAA,YACE,SAAS;AAAA,YACT,OAAO;AAAA,UACT;AAAA,UACA;AAAA,YACE,SAAS;AAAA,YACT,OAAO;AAAA,UACT;AAAA,UACA;AAAA,YACE,SAAS;AAAA,YACT,OAAO;AAAA,UACT;AAAA,UACA;AAAA,YACE,SAAS;AAAA,YACT,OAAO;AAAA,UACT;AAAA,UACA;AAAA,YACE,SAAS;AAAA,YACT,OAAO;AAAA,UACT;AAAA,QACF;AAAA,QACA,iBAAiB;AAAA,UACf;AAAA,YACE,SACE;AAAA,YACF,YAAY;AAAA,YACZ,QAAQ;AAAA,cACN,cAAc;AAAA,gBACZ,SAAS;AAAA,gBACT,OAAO;AAAA,cACT;AAAA,cACA,OAAO;AAAA,YACT;AAAA,UACF;AAAA,UACA;AAAA,YACE,SAAS;AAAA,YACT,QAAQ;AAAA,cACN,cAAc;AAAA,gBACZ,SAAS;AAAA,gBACT,OAAO;AAAA,cACT;AAAA,cACA,OAAO;AAAA,YACT;AAAA,UACF;AAAA,QACF;AAAA,QACA,aAAa;AAAA,QACb,MAAM;AAAA,MACR;AAAA,IACF;AAAA;AAAA;", "names": []}