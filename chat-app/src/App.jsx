import { useState } from 'react'
import './App.css'

// Mock chat data
const mockChats = [
  {
    id: 1,
    title: "AI Assistant Help",
    messages: [
      { id: 1, text: "Hello! How can I help you today?", sender: "ai", timestamp: "10:00 AM" },
      { id: 2, text: "I need help with React components", sender: "user", timestamp: "10:01 AM" },
      { id: 3, text: "I'd be happy to help! What specifically about React components would you like to know?", sender: "ai", timestamp: "10:01 AM" }
    ]
  },
  {
    id: 2,
    title: "Code Review",
    messages: [
      { id: 4, text: "Can you review my JavaScript code?", sender: "user", timestamp: "9:30 AM" },
      { id: 5, text: "Of course! Please share your code and I'll take a look.", sender: "ai", timestamp: "9:31 AM" }
    ]
  },
  {
    id: 3,
    title: "Project Planning",
    messages: [
      { id: 6, text: "I'm starting a new web project", sender: "user", timestamp: "Yesterday" },
      { id: 7, text: "That's exciting! What kind of web project are you planning?", sender: "ai", timestamp: "Yesterday" }
    ]
  }
]

function App() {
  const [selectedChat, setSelectedChat] = useState(mockChats[0])
  const [newMessage, setNewMessage] = useState('')

  const handleSendMessage = () => {
    if (newMessage.trim()) {
      const userMessage = {
        id: Date.now(),
        text: newMessage,
        sender: "user",
        timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
      }
      
      const aiResponse = {
        id: Date.now() + 1,
        text: "Thanks for your message! This is a mock response from the AI assistant.",
        sender: "ai",
        timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
      }

      setSelectedChat(prev => ({
        ...prev,
        messages: [...prev.messages, userMessage, aiResponse]
      }))
      
      setNewMessage('')
    }
  }

  const handleKeyDown = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSendMessage()
    }
  }

  return (
    <div className="app">
      {/* Chat History Sidebar */}
      <div className="sidebar">
        <div className="sidebar-header">
          <h2>Chat History</h2>
        </div>
        <div className="chat-list">
          {mockChats.map(chat => (
            <div
              key={chat.id}
              className={`chat-item ${selectedChat.id === chat.id ? 'active' : ''}`}
              onClick={() => setSelectedChat(chat)}
            >
              <div className="chat-title">{chat.title}</div>
              <div className="chat-preview">
                {chat.messages[chat.messages.length - 1]?.text.substring(0, 50)}...
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Main Chat Window */}
      <div className="main-chat">
        <div className="chat-header">
          <h3>{selectedChat.title}</h3>
        </div>
        
        <div className="messages-container">
          {selectedChat.messages.map(message => (
            <div
              key={message.id}
              className={`message ${message.sender === 'user' ? 'user-message' : 'ai-message'}`}
            >
              <div className="message-content">
                <div className="message-text">{message.text}</div>
                <div className="message-timestamp">{message.timestamp}</div>
              </div>
            </div>
          ))}
        </div>

        <div className="input-container">
          <textarea
            value={newMessage}
            onChange={(e) => setNewMessage(e.target.value)}
            onKeyDown={handleKeyDown}
            placeholder="Type your message here..."
            className="message-input"
            rows="3"
          />
          <button 
            onClick={handleSendMessage}
            className="send-button"
            disabled={!newMessage.trim()}
          >
            Send
          </button>
        </div>
      </div>
    </div>
  )
}

export default App