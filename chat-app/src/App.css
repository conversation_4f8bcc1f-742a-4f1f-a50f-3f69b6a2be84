* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  background-color: #f5f5f5;
}

.app {
  display: flex;
  height: 100vh;
  max-width: 1200px;
  margin: 0 auto;
  background: white;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
}

/* Sidebar Styles */
.sidebar {
  width: 300px;
  background: #f8f9fa;
  border-right: 1px solid #e9ecef;
  display: flex;
  flex-direction: column;
}

.sidebar-header {
  padding: 20px;
  border-bottom: 1px solid #e9ecef;
  background: white;
}

.sidebar-header h2 {
  font-size: 18px;
  font-weight: 600;
  color: #343a40;
}

.chat-list {
  flex: 1;
  overflow-y: auto;
  padding: 10px 0;
}

.chat-item {
  padding: 15px 20px;
  cursor: pointer;
  border-bottom: 1px solid #f1f3f4;
  transition: background-color 0.2s;
}

.chat-item:hover {
  background-color: #e9ecef;
}

.chat-item.active {
  background-color: #007bff;
  color: white;
}

.chat-title {
  font-weight: 600;
  font-size: 14px;
  margin-bottom: 5px;
}

.chat-preview {
  font-size: 12px;
  color: #6c757d;
  opacity: 0.8;
}

.chat-item.active .chat-preview {
  color: rgba(255, 255, 255, 0.8);
}

/* Main Chat Area */
.main-chat {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.chat-header {
  padding: 20px 25px;
  border-bottom: 1px solid #e9ecef;
  background: white;
}

.chat-header h3 {
  font-size: 18px;
  font-weight: 600;
  color: #343a40;
}

.messages-container {
  flex: 1;
  overflow-y: auto;
  padding: 20px 25px;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.message {
  display: flex;
  max-width: 70%;
}

.user-message {
  align-self: flex-end;
}

.ai-message {
  align-self: flex-start;
}

.message-content {
  padding: 12px 16px;
  border-radius: 18px;
  position: relative;
}

.user-message .message-content {
  background: #007bff;
  color: white;
  border-bottom-right-radius: 6px;
}

.ai-message .message-content {
  background: #f8f9fa;
  color: #343a40;
  border: 1px solid #e9ecef;
  border-bottom-left-radius: 6px;
}

.message-text {
  font-size: 14px;
  line-height: 1.4;
  margin-bottom: 4px;
}

.message-timestamp {
  font-size: 10px;
  opacity: 0.7;
  text-align: right;
}

.ai-message .message-timestamp {
  text-align: left;
}

/* Input Area */
.input-container {
  padding: 20px 25px;
  border-top: 1px solid #e9ecef;
  background: white;
  display: flex;
  gap: 10px;
  align-items: flex-end;
}

.message-input {
  flex: 1;
  border: 1px solid #ced4da;
  border-radius: 20px;
  padding: 12px 16px;
  font-size: 14px;
  resize: none;
  outline: none;
  font-family: inherit;
}

.message-input:focus {
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.send-button {
  background: #007bff;
  color: white;
  border: none;
  border-radius: 20px;
  padding: 12px 20px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s;
}

.send-button:hover:not(:disabled) {
  background: #0056b3;
}

.send-button:disabled {
  background: #6c757d;
  cursor: not-allowed;
}

/* Responsive Design */
@media (max-width: 768px) {
  .app {
    height: 100vh;
  }
  
  .sidebar {
    width: 250px;
  }
  
  .message {
    max-width: 85%;
  }
  
  .input-container {
    padding: 15px 20px;
  }
  
  .messages-container {
    padding: 15px 20px;
  }
}

@media (max-width: 640px) {
  .app {
    flex-direction: column;
  }
  
  .sidebar {
    width: 100%;
    height: 200px;
    border-right: none;
    border-bottom: 1px solid #e9ecef;
  }
  
  .main-chat {
    height: calc(100vh - 200px);
  }
  
  .message {
    max-width: 90%;
  }
}